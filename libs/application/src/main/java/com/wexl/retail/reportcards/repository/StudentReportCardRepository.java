package com.wexl.retail.reportcards.repository;

import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.reportcards.model.ReportCardJob;
import com.wexl.retail.reportcards.model.StudentReportCard;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentReportCardRepository extends JpaRepository<StudentReportCard, Long> {

  Optional<StudentReportCard> findByStudentIdAndReportCardJob(
      Long student, ReportCardJob currentJob);

  List<StudentReportCard> findByReportCardJob(ReportCardJob currentJob);

  List<StudentReportCard> findByReportCardConfigAndOfflineTestDefinitionIdAndTemplateType(
      String config, Long otdId, ReportCardTemplateType templateType);

  Optional<StudentReportCard> findByStudentIdAndReportCardTemplate(
      Long student, Long reportCardTemplate);
}
