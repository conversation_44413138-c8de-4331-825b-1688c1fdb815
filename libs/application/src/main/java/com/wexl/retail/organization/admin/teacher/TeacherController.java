package com.wexl.retail.organization.admin.teacher;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.metrics.reportcards.WeeklyReportService;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/orgs/{orgId}/teachers")
@RestController
@Slf4j
@RequiredArgsConstructor
public class TeacherController {

  private final TeacherService teacherService;
  private final AuthService authService;
  private final WeeklyReportService weeklyReportService;
  private final UserRoleHelper userRoleHelper;

  @IsOrgAdmin
  @PostMapping
  public ResponseEntity<TeacherResponse> createTeacher(
      @PathVariable String orgId, @RequestBody @Valid TeacherRequest teacherRequest) {
    try {
      teacherService.createTeacher(orgId, teacherRequest);
      return ResponseEntity.status(HttpStatus.CREATED).build();
    } catch (ApiException apiException) {
      throw apiException;
    } catch (Exception exception) {
      log.error("Failed to create teacher", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "Failed to create teacher");
    }
  }

  @GetMapping
  public List<TeacherResponse> getTeachers(
      @PathVariable String orgId, @RequestParam(required = false) String otherOrg) {
    User user = authService.getUserDetails();

    if (Boolean.TRUE.equals(userRoleHelper.isManager(user))) {
      Teacher teacher = weeklyReportService.getTeacherByUser(user);
      var childOrgs = teacher.getChildOrgs();
      return teacherService.getChildOrgTeachersDetails(childOrgs);
    } else if (Boolean.TRUE.equals(userRoleHelper.isOrgAdmin(user))) {
      if (Objects.nonNull(otherOrg)) {
        return teacherService.getTeachers(otherOrg);
      }
      return teacherService.getTeachers(orgId);
    }
    return teacherService.getTeachers(orgId);
  }

  @GetMapping("/metrics")
  public List<TeacherDto.TeacherResponseWithMetrics> getAllTeachers(@PathVariable String orgId) {
    return teacherService.getAllTeacherDetailsWithMetrics(orgId);
  }

  @IsOrgAdmin
  @GetMapping("/{teacherId}")
  public ResponseEntity<TeacherResponse> getTeacher(
      @PathVariable String orgId, @PathVariable String teacherId) {
    try {
      return ResponseEntity.ok(teacherService.getTeacher(orgId, teacherId));
    } catch (Exception exception) {
      log.error("Failed to get teacher information", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "Failed to get teacher information");
    }
  }

  @IsOrgAdmin
  @PutMapping("/{teacherId}")
  public ResponseEntity<TeacherResponse> editTeacher(
      @PathVariable String orgId,
      @PathVariable String teacherId,
      @RequestBody @Valid TeacherRequest teacherRequest) {
    try {
      return ResponseEntity.ok(teacherService.editTeacher(orgId, teacherId, teacherRequest));
    } catch (Exception exception) {
      log.error("Failed to update teacher information", exception);
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "Failed to update teacher information");
    }
  }

  @IsOrgAdmin
  @DeleteMapping("/{teacherId}")
  public ResponseEntity<Object> deleteTeacher(
      @PathVariable String orgId, @PathVariable("teacherId") String teacherAuthId) {
    try {
      teacherService.deleteTeacher(orgId, teacherAuthId);
      return ResponseEntity.ok().build();
    } catch (Exception exception) {
      log.error("Failed to delete teacher", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "Failed to delete teacher");
    }
  }
}
