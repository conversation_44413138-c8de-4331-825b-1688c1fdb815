package com.wexl.retail.model;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "user_badge")
@AllArgsConstructor
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class UserBadge extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_badge_id_seq")
  @SequenceGenerator(name = "user_badge_id_seq", allocationSize = 1)
  private Long id;

  @ManyToOne private Badge badge;

  @ManyToOne private User user;
  private Date issuedDate;
  private boolean isVisible;
  private Date expiryDate;

  @Type(JsonType.class)
  @Column(name = "badge_context", columnDefinition = "jsonb")
  private List<UserBadgeContext> context;
}
