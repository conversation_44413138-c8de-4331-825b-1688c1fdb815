package com.wexl.retail.notifications.service;

import com.wexl.retail.generic.ProfileUtils;
import com.wexl.retail.notifications.model.ScheduledEmail;
import com.wexl.retail.notifications.repository.ScheduledEmailRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.email.scheduler", havingValue = "true")
public class SendScheduledEmails {
  private final ScheduledEmailRepository scheduledEmailRepository;
  private final ProfileUtils profileUtils;
  private final EmailNotificationService emailNotificationService;

  @Value("${app.allowed.test.orgSlug:}")
  private String allowedTestOrgSlug;

  @Scheduled(fixedRate = 60000)
  @SchedulerLock(
      name = "sendScheduledEmails",
      lockAtMostFor = "10m", // ensure lock doesn't stay forever
      lockAtLeastFor = "1m") // optional smoothing
  public void processScheduledEmails() {
    LockAssert.assertLocked(); // fail-fast if no lock is held

    List<ScheduledEmail> dueEmails =
        scheduledEmailRepository.findBySentFalseAndScheduledTimeBefore(LocalDateTime.now());
    if (dueEmails.isEmpty()) return;

    if (profileUtils.isDev() && StringUtils.isNotBlank(allowedTestOrgSlug)) {
      dueEmails =
          dueEmails.stream()
              .filter(email -> allowedTestOrgSlug.equals(email.getOrgSlug()))
              .collect(Collectors.toList());
    } else if (profileUtils.isDev()) {
      log.warn("Dev profile active but no allowed test orgSlug configured. Skipping all emails.");
      return;
    }

    emailNotificationService.processDueEmails(dueEmails);
  }
}
