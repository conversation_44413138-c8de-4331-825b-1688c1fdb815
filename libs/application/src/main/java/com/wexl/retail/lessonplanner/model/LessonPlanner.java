package com.wexl.retail.lessonplanner.model;

import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "lesson_planners")
public class LessonPlanner extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "chapter_slug")
  private String chapterSlug;

  @Column(name = "subtopic_slug")
  private String subtopicSlug;

  @Column(name = "lecture_youtube_url")
  private String lectureYoutubeUrl;

  @Column(name = "lecture_video")
  private String lectureVideo;

  @Column(name = "paragraph")
  private String paragraph;

  @Type(JsonType.class)
  @Column(name = "attachments", columnDefinition = "jsonb")
  private List<String> attachments;

  @Column(name = "org_slug")
  private String orgSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_timetable_detail_id")
  private TeacherTimeTableDetail teacherTimeTableDetail;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "lessonPlanner", cascade = CascadeType.ALL)
  private List<LessonPlannerDetails> lessonPlannerDetails;
}
