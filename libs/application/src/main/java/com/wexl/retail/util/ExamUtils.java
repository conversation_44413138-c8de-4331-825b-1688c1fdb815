package com.wexl.retail.util;

import static com.wexl.retail.student.exam.migration.ExamMigrationService.MARKS_SCORED;
import static org.apache.commons.lang3.StringUtils.trim;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.test.school.dto.PbqDto;
import com.wexl.retail.test.school.dto.QuestionDto;
import java.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
public class ExamUtils {

  private ExamUtils() {}

  public static Float setPercentage(Exam exam) {

    if (Objects.isNull(exam) || exam.getTotalMarks() == null) {
      return 0f;
    } else if (exam.getTotalMarks() == 0) {
      return 0f;
    } else {
      return (exam.getMarksScored()) / (exam.getTotalMarks()) * 100;
    }
  }

  private Map<String, Float> mapMcqMarkScored(
      QuestionDto.Mcq mcq, Integer studentAnswers, Integer marks, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers)) {
      map.put(MARKS_SCORED, 0F);
    } else if ((long) mcq.answer() == studentAnswers) {
      map.put(MARKS_SCORED, Float.valueOf(marks));
    } else {
      map.put(MARKS_SCORED, 0F);
    }
    return map;
  }

  private Map<String, Float> mapFbqMarkScored(
      QuestionDto.Fbq fbq, String studentAnswers, Integer marks, Map<String, Float> map) {
    final String fbqSelectedAnswer = trim(studentAnswers);
    if (Objects.isNull(fbqSelectedAnswer)) {
      map.put(MARKS_SCORED, 0F);
    } else {
      final String fbqAnswer = trim(fbq.answer());
      if (Objects.equals(fbqSelectedAnswer.toLowerCase(), fbqAnswer.toLowerCase())) {
        map.put(MARKS_SCORED, Float.valueOf(marks));
      } else {
        map.put(MARKS_SCORED, 0F);
      }
    }
    return map;
  }

  private Map<String, Float> mapPbqMarkScored(
      List<QuestionDto.Pbq> answers,
      PbqDto.Data studentAnswers,
      Integer totalMarks,
      Map<String, Float> map) {
    if (studentAnswers != null) {
      var pbqAnswers = studentAnswers.answers();

      List<Float> markObtained = new ArrayList<>();
      var marks = (float) totalMarks / pbqAnswers.size();
      pbqAnswers.forEach(
          pbqAnswer -> {
            if (pbqAnswer.type() != QuestionType.MCQ) {
              throw new ApiException(
                  InternalErrorCodes.INVALID_REQUEST, "error.UnsupportedQuestionType");
            }
            var mcqAnswer = getMcqAnswer(answers, pbqAnswer.mcq());
            markObtained.add(
                pbqAnswer.mcq().selectedAnswer() == null
                    ? 0
                    : getMcqMarkScored(pbqAnswer.mcq().selectedAnswer(), mcqAnswer, marks));
          });

      var marksSum = (float) markObtained.stream().mapToDouble(Float::doubleValue).sum();

      map.put(MARKS_SCORED, marksSum);
    } else {
      map.put(MARKS_SCORED, 0F);
    }
    return map;
  }

  private Float getMcqMarkScored(Integer selectedAnswer, Long answer, Float marks) {
    return Objects.equals((long) selectedAnswer, answer) ? marks : 0f;
  }

  private Long getMcqAnswer(List<QuestionDto.Pbq> testAnswers, PbqDto.Mcq mcq) {
    var mcqanswer =
        testAnswers.stream()
            .filter(mcq1 -> mcq1.uuid().equals(mcq.questionUuid()))
            .findFirst()
            .orElseThrow();
    return (long) mcqanswer.mcq().answer();
  }

  public Map<String, Float> calculateMarks(
      QuestionDto.Question testQuestion, QuestionDto.StudentQuestionStatusResponse studentAnswers) {
    Map<String, Float> map = new HashMap<>();
    if (QuestionType.MCQ.equals(studentAnswers.questionType())) {
      mapMcqMarkScored(
          testQuestion.mcq(), studentAnswers.mcqSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.FBQ.equals(studentAnswers.questionType())) {
      mapFbqMarkScored(
          testQuestion.fbq(), studentAnswers.fbqSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.PBQ.equals(studentAnswers.questionType())) {
      mapPbqMarkScored(
          testQuestion.pbq(), studentAnswers.pbqSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.AMCQ.equals(studentAnswers.questionType())) {
      mapAmcqMarksScored(
          testQuestion.amcq(), studentAnswers.amcqSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.SPCH.equals(studentAnswers.questionType())) {
      mapSpchMarkScored(
          testQuestion.spch(), studentAnswers.spchSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.NAT.equals(studentAnswers.questionType())) {
      mapNATMarkScored(
          testQuestion.nat(), studentAnswers.spchSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.MSQ.equals(studentAnswers.questionType())) {
      mapMsqMarkScored(
          testQuestion.msq(), studentAnswers.msqSelectedAnswer(), testQuestion.marks(), map);
    } else if (QuestionType.YESNO.equals(studentAnswers.questionType())) {
      mapYesNoMarkScored(
          testQuestion.yesNo(), studentAnswers.yesNoSelectedAnswer(), testQuestion.marks(), map);
    }
    return map;
  }

  private Map<String, Float> mapAmcqMarksScored(
      QuestionDto.Amcq testQuestion,
      Integer studentAnswers,
      Integer totalMarks,
      Map<String, Float> map) {
    if (Objects.isNull(studentAnswers)) {
      map.put(MARKS_SCORED, 0F);
    } else if (Objects.equals(studentAnswers, testQuestion.answer())) {
      map.put(MARKS_SCORED, Float.valueOf(totalMarks));
    } else {
      map.put(MARKS_SCORED, 0F);
    }
    return map;
  }

  private Map<String, Float> mapSpchMarkScored(
      QuestionDto.Spch testQuestion,
      String studentAnswers,
      Integer totalMarks,
      Map<String, Float> map) {
    if (Objects.isNull(studentAnswers)) {
      map.put(MARKS_SCORED, 0F);
    } else if (Objects.nonNull(testQuestion.answerAudioPath())) {
      map.put(MARKS_SCORED, Float.valueOf(totalMarks));
    }
    return map;
  }

  private Map<String, Float> mapNATMarkScored(
      QuestionDto.Nat testQuestion,
      String studentAnswers,
      Integer totalMarks,
      Map<String, Float> map) {
    if (Objects.isNull(studentAnswers)) {
      map.put(MARKS_SCORED, 0F);
    } else if (Objects.equals(studentAnswers, testQuestion.answer())) {
      map.put(MARKS_SCORED, Float.valueOf(totalMarks));
    } else {
      map.put(MARKS_SCORED, 0F);
    }
    return map;
  }

  private void mapMsqMarkScored(
      QuestionDto.Msq testQuestion,
      List<Long> studentAnswers,
      Integer totalMarks,
      Map<String, Float> map) {
    if (Objects.isNull(studentAnswers)) {
      map.put(MARKS_SCORED, 0F);
    } else if (new HashSet<>(testQuestion.answers()).containsAll(studentAnswers)
        && testQuestion.answers().size() == studentAnswers.size()) {
      map.put(MARKS_SCORED, Float.valueOf(totalMarks));
    } else {
      map.put(MARKS_SCORED, 0F);
    }
  }

  private void mapYesNoMarkScored(
      QuestionDto.YesNo testQuestion,
      Boolean studentAnswers,
      Integer totalMarks,
      Map<String, Float> map) {
    if (Objects.isNull(studentAnswers)) {
      map.put(MARKS_SCORED, 0F);
    } else if (Objects.equals(studentAnswers, testQuestion.answer())) {
      map.put(MARKS_SCORED, Float.valueOf(totalMarks));
    } else {
      map.put(MARKS_SCORED, 0F);
    }
  }
}
