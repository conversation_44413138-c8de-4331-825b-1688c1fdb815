package com.wexl.retail.qrcode.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum QrCodeStatus {
  REGISTERED("REGISTERED"),
  NOT_REGISTERED("NOT_REGISTERED");

  private final String value;

  public static QrCodeStatus fromValue(String value) {
    if (value == null || "".equals(value)) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (QrCodeStatus enumEntry : QrCodeStatus.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
