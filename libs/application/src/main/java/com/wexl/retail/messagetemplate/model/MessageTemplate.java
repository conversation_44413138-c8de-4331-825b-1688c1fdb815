package com.wexl.retail.messagetemplate.model;

import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.messagetemplate.dto.MessageType;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "message_templates", schema = "public")
public class MessageTemplate extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private MessageType type;
  private String message;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "message_template_category")
  private MessageTemplateCategory messageTemplateCategory;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "sms_dlt_template_id")
  private String smsDltTemplateId;

  @Column(name = "whatsapp_template_id")
  private String whatsAppTemplateId;

  @Column(name = "email_template_id")
  private String emailTemplateId;

  @Column(name = "attachment")
  private String attachment;

  @Column(name = "title")
  private String title;
}
