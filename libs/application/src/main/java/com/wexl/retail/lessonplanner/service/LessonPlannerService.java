package com.wexl.retail.lessonplanner.service;

import com.wexl.retail.lessonplanner.dto.LessonPlannerDto;
import com.wexl.retail.lessonplanner.model.LessonPlanner;
import com.wexl.retail.lessonplanner.model.LessonPlannerDetails;
import com.wexl.retail.lessonplanner.model.TeacherTimeTableDetail;
import com.wexl.retail.lessonplanner.repository.LessonPlannerDetailRepository;
import com.wexl.retail.lessonplanner.repository.LessonPlannerRepository;
import com.wexl.retail.lessonplanner.repository.LessonPlannerTemplateFieldRepository;
import com.wexl.retail.lessonplanner.repository.LessonPlannerTemplateRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class LessonPlannerService {
  private final LessonPlannerRepository lessonPlannerRepository;
  private final LessonPlannerDetailRepository lessonPlannerDetailsRepository;
  private final TeacherTimeTableService teacherTimeTableService;
  private final LessonPlannerTemplateRepository lessonPlannerTemplateRepository;
  private final LessonPlannerTemplateFieldRepository lessonPlannerTemplateFieldRepository;
  private final NotificationsService notificationsService;

  @Transactional
  public void createLessonPlan(String orgSlug, LessonPlannerDto.LessonPlanRequest request) {

    TeacherTimeTableDetail teacherTimeTableDetail =
        teacherTimeTableService.validateTeacherTimeTableDetail(request.teacherTimeTableDetailId());
    List<String> attachments = request.attachment() == null ? null : List.of(request.attachment());
    LessonPlanner lessonPlanner =
        LessonPlanner.builder()
            .orgSlug(orgSlug)
            .chapterSlug(request.chapterSlug())
            .subtopicSlug(request.subtopicSlug())
            .lectureYoutubeUrl(request.youtubeUrl())
            .lectureVideo(request.lectureVideo())
            .attachments(attachments)
            .paragraph(request.paragraph())
            .teacherTimeTableDetail(teacherTimeTableDetail)
            .build();

    List<LessonPlannerDetails> details =
        buildLessonPlannerDetails(lessonPlanner, request.fieldRequests());
    lessonPlanner.setLessonPlannerDetails(details);
    lessonPlannerRepository.save(lessonPlanner);
  }

  private List<LessonPlannerDetails> buildLessonPlannerDetails(
      LessonPlanner lessonPlanner, List<LessonPlannerDto.TemplateFieldRequest> fieldRequests) {
    List<LessonPlannerDetails> details = new ArrayList<>();
    fieldRequests.forEach(
        request -> {
          var lessonPlannerTemplateField =
              lessonPlannerTemplateFieldRepository.findById(request.fieldId()).orElseThrow();
          details.add(
              LessonPlannerDetails.builder()
                  .lessonPlanner(lessonPlanner)
                  .lessonPlannerTemplateField(lessonPlannerTemplateField)
                  .definition(request.definition())
                  .build());
        });
    return lessonPlannerDetailsRepository.saveAll(details);
  }

  public LessonPlannerDto.LessonPlannerResponse getLessonPlanById(Long lessonPlanId) {
    var lessonPlanner = validateLessonPlanner(lessonPlanId);
    var attachment =
        Objects.isNull(lessonPlanner.getAttachments())
            ? null
            : lessonPlanner.getAttachments().getFirst();
    return LessonPlannerDto.LessonPlannerResponse.builder()
        .id(lessonPlanner.getId())
        .chapterSlug(lessonPlanner.getChapterSlug())
        .subtopicSlug(lessonPlanner.getSubtopicSlug())
        .youtubeUrl(lessonPlanner.getLectureYoutubeUrl())
        .lectureVideoPath(lessonPlanner.getLectureVideo())
        .attachmentPath(attachment)
        .lectureVideo(
            Objects.isNull(lessonPlanner.getLectureVideo())
                ? null
                : notificationsService
                    .convertAttachmentToS3Link(
                        Collections.singletonList(lessonPlanner.getLectureVideo()))
                    .getFirst())
        .attachment(
            Objects.isNull(attachment)
                ? null
                : notificationsService
                    .convertAttachmentToS3Link(Collections.singletonList(attachment))
                    .getFirst())
        .paragraph(lessonPlanner.getParagraph())
        .templateId(
            lessonPlanner.getLessonPlannerDetails().isEmpty()
                ? null
                : lessonPlanner
                    .getLessonPlannerDetails()
                    .getFirst()
                    .getLessonPlannerTemplateField()
                    .getId())
        .fieldResponses(buildFieldResponses(lessonPlanner.getLessonPlannerDetails()))
        .build();
  }

  private List<LessonPlannerDto.TemplateFieldResponse> buildFieldResponses(
      List<LessonPlannerDetails> lessonPlannerDetails) {
    List<LessonPlannerDto.TemplateFieldResponse> responses = new ArrayList<>();
    if (lessonPlannerDetails.isEmpty()) {
      return responses;
    }
    lessonPlannerDetails.forEach(
        detail ->
            responses.add(
                LessonPlannerDto.TemplateFieldResponse.builder()
                    .lessonPlannerDetailId(detail.getId())
                    .fieldId(detail.getLessonPlannerTemplateField().getId())
                    .fieldName(detail.getLessonPlannerTemplateField().getFieldName())
                    .definition(detail.getDefinition())
                    .build()));
    return responses;
  }

  public void updateLessonPlan(Long lessonPlanId, LessonPlannerDto.LessonPlanRequest request) {
    var lessonPlanner = validateLessonPlanner(lessonPlanId);
    lessonPlanner.setChapterSlug(request.chapterSlug());
    lessonPlanner.setSubtopicSlug(request.subtopicSlug());
    lessonPlanner.setLectureYoutubeUrl(request.youtubeUrl());
    lessonPlanner.setLectureVideo(request.lectureVideo());
    lessonPlanner.setAttachments(List.of(request.attachment()));
    lessonPlanner.setParagraph(request.paragraph());
    updateLessonPlannerDetails(request.fieldRequests());
    lessonPlannerRepository.save(lessonPlanner);
  }

  private void updateLessonPlannerDetails(
      List<LessonPlannerDto.TemplateFieldRequest> templateFieldRequests) {
    List<LessonPlannerDetails> lessonPlannerDetails = new ArrayList<>();
    templateFieldRequests.forEach(
        request -> {
          var lessonPlannerDetail = validateLessonPlannerDetails(request.lessonPlannerDetailId());
          lessonPlannerDetail.setDefinition(request.definition());
          lessonPlannerDetails.add(lessonPlannerDetail);
        });
    lessonPlannerDetailsRepository.saveAll(lessonPlannerDetails);
  }

  @NotNull
  private LessonPlannerDetails validateLessonPlannerDetails(long id) {
    return lessonPlannerDetailsRepository.findById(id).orElseThrow();
  }

  @NotNull
  private LessonPlanner validateLessonPlanner(Long lessonPlanId) {
    return lessonPlannerRepository.findById(lessonPlanId).orElseThrow();
  }

  public List<LessonPlannerDto.LessonPlanTemplateResponse> getLessonPlanTemplates() {
    var lessonPlannerTemplates = lessonPlannerTemplateRepository.findAll();
    List<LessonPlannerDto.LessonPlanTemplateResponse> responses = new ArrayList<>();
    lessonPlannerTemplates.forEach(
        template ->
            responses.add(
                LessonPlannerDto.LessonPlanTemplateResponse.builder()
                    .id(template.getId())
                    .name(template.getName())
                    .fieldResponses(
                        template.getLessonPlannerTemplateFields().stream()
                            .map(
                                field ->
                                    LessonPlannerDto.TemplateFieldResponse.builder()
                                        .fieldId(field.getId())
                                        .fieldName(field.getFieldName())
                                        .definition(field.getDefinition())
                                        .build())
                            .toList())
                    .build()));
    return responses;
  }

  public void deleteLessonPlan(Long lessonPlanId) {
    lessonPlannerRepository.deleteById(lessonPlanId);
  }
}
