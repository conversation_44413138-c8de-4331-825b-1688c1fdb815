package com.wexl.retail.lessonplanner.dto;

import java.util.List;
import lombok.Builder;

public record LessonPlannerDto() {

  public record LessonPlanRequest(
      Long teacherTimeTableDetailId,
      String chapterSlug,
      String subtopicSlug,
      String youtubeUrl,
      String lectureVideo,
      String attachment,
      String paragraph,
      List<TemplateFieldRequest> fieldRequests) {}

  public record TemplateFieldRequest(Long lessonPlannerDetailId, Long fieldId, String definition) {}

  @Builder
  public record LessonPlannerResponse(
      Long id,
      String chapterSlug,
      String subtopicSlug,
      String youtubeUrl,
      String lectureVideoPath,
      String attachmentPath,
      String lectureVideo,
      String attachment,
      String paragraph,
      Long templateId,
      List<TemplateFieldResponse> fieldResponses) {}

  @Builder
  public record TemplateFieldResponse(
      Long lessonPlannerDetailId, Long fieldId, String fieldName, String definition) {}

  @Builder
  public record LessonPlanTemplateResponse(
      Long id, String name, List<TemplateFieldResponse> fieldResponses) {}
}
