package com.wexl.retail.mlp.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.model.KMData;
import com.wexl.retail.mlp.model.StudentKMeter;
import com.wexl.retail.mlp.repository.KnowledgeMeterRepository;
import com.wexl.retail.mlp.repository.StudentKMeterRepository;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class KnowledgeMeterService {

  private final KnowledgeMeterRepository knowledgeMeterRepository;
  private final OrganizationRepository organizationRepository;
  private final StrapiService strapiService;

  private final ExamService examService;

  private final AuthService authService;

  private final StudentKMeterRepository studentKMeterRepository;

  public KMGenericSectionResponse getKmSectionSummary(String orgSlug, String sectionUuid) {

    var kmSummaryList = knowledgeMeterRepository.getSectionSummaryAttendance(orgSlug, sectionUuid);

    var sectionSummaryAttendance = calculateSectionSummaryAttendance(kmSummaryList);
    var sectionSummaryKnowledge = calculateSectionSummaryKnowledge(kmSummaryList);
    return KMGenericSectionResponse.builder()
        .attendanceMeterList(sectionSummaryAttendance)
        .knowledgeMeterList(sectionSummaryKnowledge)
        .build();
  }

  public List<SubjectDetailResponse> calculateSectionSummaryAttendance(
      List<KmSummary> kmSummaryList) {
    List<SubjectDetailResponse> subjectDetailResponseList = new ArrayList<>();
    var subjectList = kmSummaryList.stream().map(KmSummary::getSubjectSlug).distinct().toList();

    for (var subject : subjectList) {
      var subjectData =
          kmSummaryList.stream().filter(s -> s.getSubjectSlug().equals(subject)).toList();
      var chapterNamesList =
          subjectData.stream().map(KmSummary::getChapterName).distinct().toList();

      var totalAttendancePercentage =
          Double.parseDouble(
              Constants.DECIMAL_FORMAT.format(
                  (subjectData.stream().mapToDouble(KmSummary::getAttendancePercentage).sum())
                      / subjectData.size()));

      var chapterList = calculateChapterData(chapterNamesList, subjectData);
      subjectDetailResponseList.add(
          SubjectDetailResponse.builder()
              .name(subjectData.getFirst().getSubjectName())
              .slug(subject)
              .attendancePercentage(totalAttendancePercentage)
              .chapterSummaryList(chapterList)
              .build());
    }
    return subjectDetailResponseList;
  }

  public List<SubjectDetailResponse> calculateSectionSummaryKnowledge(
      List<KmSummary> kmSummaryList) {
    List<SubjectDetailResponse> subjectDetailResponseList = new ArrayList<>();
    var subjects = kmSummaryList.stream().filter(s -> !s.getSubtopicName().isEmpty()).toList();
    var subjectList = subjects.stream().map(KmSummary::getSubjectSlug).distinct().toList();
    for (var subject : subjectList) {
      var subjectData = subjects.stream().filter(s -> s.getSubjectSlug().equals(subject)).toList();
      var chapterNamesList =
          subjectData.stream().map(KmSummary::getChapterName).distinct().toList();

      var totalKnowledgePercentage =
          Double.parseDouble(
              Constants.DECIMAL_FORMAT.format(
                  (subjectData.stream().mapToDouble(KmSummary::getKnowledgePercentage).sum())
                      / subjectData.size()));

      var chapterList = calculateChapterData(chapterNamesList, subjectData);
      subjectDetailResponseList.add(
          SubjectDetailResponse.builder()
              .name(subjectData.getFirst().getSubjectName())
              .slug(subject)
              .percentage(totalKnowledgePercentage)
              .chapterSummaryList(chapterList)
              .build());
    }
    return subjectDetailResponseList;
  }

  public List<GenericDto> calculateSubTopic(
      List<String> subtopicList, List<KmSummary> chapterList) {
    List<GenericDto> dtoList = new ArrayList<>();
    for (var subTopic : subtopicList) {
      var data =
          chapterList.stream()
              .filter(s -> Objects.equals(s.getSubtopicName(), subTopic))
              .findFirst();
      dtoList.add(
          GenericDto.builder()
              .name(Objects.nonNull(subTopic) ? subTopic : "")
              .slug(Objects.nonNull(subTopic) ? subTopic : "")
              .attendance(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          data.isPresent() ? data.get().getAttendancePercentage() : 0.0)))
              .average(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          data.isPresent() ? data.get().getKnowledgePercentage() : 0.0)))
              .build());
    }
    return dtoList;
  }

  public List<ChapterSummaryDto> calculateChapterData(
      List<String> chapterNamesList, List<KmSummary> subjectData) {
    List<ChapterSummaryDto> chapterSummaryDtoList = new ArrayList<>();
    for (var chapter : chapterNamesList) {
      var chapterList =
          subjectData.stream().filter(s -> s.getChapterName().equals(chapter)).toList();
      var subtopicList = chapterList.stream().map(KmSummary::getSubtopicName).toList();
      var dtoList = calculateSubTopic(subtopicList, chapterList);
      chapterSummaryDtoList.add(
          ChapterSummaryDto.builder()
              .name(chapter)
              .attendance(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          (dtoList.stream().mapToDouble(GenericDto::getAttendance).sum())
                              / dtoList.size())))
              .average(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          (dtoList.stream().mapToDouble(GenericDto::getAverage).sum())
                              / dtoList.size())))
              .subtopicData(dtoList)
              .build());
    }
    return chapterSummaryDtoList;
  }

  public KMGenericGradesResponse getKmGrades(String orgSlug, String grades) {
    var attendanceData = knowledgeMeterRepository.getKmGradesAttendance(orgSlug, grades);
    var knowledgeData = knowledgeMeterRepository.getKmGradesKnowledge(orgSlug, grades);

    var gradesAttendance = calculateKmGradesAttendance(attendanceData);
    var sectionSummaryKnowledge = calculateKmGradesKnowledge(knowledgeData);
    return KMGenericGradesResponse.builder()
        .attendanceMeterList(gradesAttendance)
        .knowledgeMeterList(sectionSummaryKnowledge)
        .build();
  }

  public List<KMSummaryResponse> calculateKmGradesAttendance(List<KmSummary> kmSummaryList) {
    var sectionList = kmSummaryList.stream().map(KmSummary::getName).distinct().toList();
    List<KMSummaryResponse> responseList = new ArrayList<>();

    for (var section : sectionList) {
      var kmSummaryData = kmSummaryList.stream().filter(s -> s.getName().equals(section)).toList();
      if (!kmSummaryData.isEmpty()) {
        List<KMData> kmData =
            kmSummaryData.stream()
                .map(
                    s ->
                        KMData.builder()
                            .name(s.getSubjectName())
                            .slug(s.getSubjectSlug())
                            .attendance(
                                String.valueOf(
                                    Double.parseDouble(
                                        Constants.DECIMAL_FORMAT.format(
                                            s.getAttendancePercentage()))))
                            .build())
                .toList();

        var totalAttendance = calculateTotalAttendancePercentage(kmSummaryData);
        responseList.add(
            KMSummaryResponse.builder()
                .name(section)
                .averageAttendancePercentage(
                    Double.parseDouble(
                        Constants.DECIMAL_FORMAT.format(totalAttendance.getAttendancePercentage())))
                .data(kmData)
                .build());
      }
    }
    return responseList;
  }

  public List<KMSummaryResponse> calculateKmGradesKnowledge(List<KmSummary> kmSummaryList) {
    var sectionList = kmSummaryList.stream().map(KmSummary::getName).distinct().toList();
    List<KMSummaryResponse> responseList = new ArrayList<>();

    for (var section : sectionList) {
      var kmSummaryData = kmSummaryList.stream().filter(s -> s.getName().equals(section)).toList();
      if (!kmSummaryData.isEmpty()) {
        List<KMData> kmData =
            kmSummaryData.stream()
                .map(
                    s ->
                        KMData.builder()
                            .name(s.getSubjectName())
                            .slug(s.getSubjectSlug())
                            .percentage(
                                String.valueOf(
                                    Double.parseDouble(
                                        Constants.DECIMAL_FORMAT.format(
                                            s.getKnowledgePercentage()))))
                            .build())
                .toList();

        var totalAttendance = calculateTotalAttendancePercentage(kmSummaryData);
        responseList.add(
            KMSummaryResponse.builder()
                .name(section)
                .averageKnowledgePercentage(
                    Double.parseDouble(
                        Constants.DECIMAL_FORMAT.format(totalAttendance.getKnowledgePercentage())))
                .data(kmData)
                .build());
      }
    }
    return responseList;
  }

  public MlpKnowledgeAndAttendanceData calculateTotalAttendancePercentage(
      List<KmSummary> kmSummaryData) {
    double knowledgePercentage = 0.0;
    double sectionAttendance = 0.0;
    double totalSectionPercentage = 0.0;
    double totalKnowledgePercentage = 0.0;
    MlpKnowledgeAndAttendanceData mlp = new MlpKnowledgeAndAttendanceData();
    if (!kmSummaryData.isEmpty()) {
      for (var data : kmSummaryData) {
        knowledgePercentage +=
            data.getKnowledgePercentage() == null ? 0.0 : data.getKnowledgePercentage();
        sectionAttendance +=
            data.getAttendancePercentage() == null ? 0.0 : data.getAttendancePercentage();
      }
      totalSectionPercentage = sectionAttendance / kmSummaryData.size();
      totalKnowledgePercentage = knowledgePercentage / kmSummaryData.size();
    }
    mlp.setAttendancePercentage(totalSectionPercentage);
    mlp.setKnowledgePercentage(totalKnowledgePercentage);

    return mlp;
  }

  public KMGenericSummaryResponse getKmSummary(
      List<KMGradesRequest> gradesRequest, String orgSlug) {

    var gradeSlugs = gradesRequest.stream().map(KMGradesRequest::getGradeSlug).toList();

    var gradeEntity = strapiService.getAllGrades();

    Map<String, String> gradeMap = new HashMap<>();
    gradeEntity.forEach(entity -> gradeMap.put(entity.getSlug(), entity.getName()));
    var attendanceData = knowledgeMeterRepository.getKmSummaryAttendance(orgSlug, gradeSlugs);
    var knowledgeData = knowledgeMeterRepository.getKmSummaryKnowledge(orgSlug, gradeSlugs);

    var sectionSummaryAttendance =
        calculateKmSummaryAttendance(attendanceData, orgSlug, gradesRequest);
    var sectionSummaryKnowledge =
        calculateKmSummaryKnowledge(knowledgeData, orgSlug, gradesRequest);
    return KMGenericSummaryResponse.builder()
        .attendanceMeterList(sectionSummaryAttendance)
        .knowledgeMeterList(sectionSummaryKnowledge)
        .build();
  }

  public KMSummaryResponse calculateKmSummaryAttendance(
      List<KmSummary> kmSummaryList, String orgSlug, List<KMGradesRequest> gradesRequest) {

    List<KMData> gradeWisePerformanceResponse = new ArrayList<>();
    Map<String, String> gradeMap = new HashMap<>();
    var organization = organizationRepository.findBySlug(orgSlug);
    for (var data : gradesRequest) {
      String attendancePercentage = "-";

      var kmData =
          kmSummaryList.stream()
              .filter(s -> s.getGradeSlug().equals(data.getGradeSlug()))
              .findFirst();
      if (kmData.isPresent()) {
        attendancePercentage =
            String.valueOf(
                Double.parseDouble(
                    Constants.DECIMAL_FORMAT.format(kmData.get().getAttendancePercentage())));
      }

      gradeWisePerformanceResponse.add(
          KMData.builder()
              .name(gradeMap.get(data.getGradeSlug()))
              .slug(data.getGradeSlug())
              .attendance(attendancePercentage)
              .build());
    }
    var totalAttendance = calculateTotalAttendancePercentage(kmSummaryList);
    return KMSummaryResponse.builder()
        .averageAttendancePercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(totalAttendance.getAttendancePercentage())))
        .data(gradeWisePerformanceResponse)
        .orgName(organization.getName())
        .build();
  }

  public KMSummaryResponse calculateKmSummaryKnowledge(
      List<KmSummary> kmSummaryList, String orgSlug, List<KMGradesRequest> gradesRequest) {

    List<KMData> gradeWisePerformanceResponse = new ArrayList<>();
    Map<String, String> gradeMap = new HashMap<>();
    var organization = organizationRepository.findBySlug(orgSlug);
    for (var data : gradesRequest) {
      String knowledgePercentage = "-";

      var kmData =
          kmSummaryList.stream()
              .filter(s -> s.getGradeSlug().equals(data.getGradeSlug()))
              .findFirst();
      if (kmData.isPresent()) {

        knowledgePercentage =
            String.valueOf(
                Double.parseDouble(
                    Constants.DECIMAL_FORMAT.format(kmData.get().getKnowledgePercentage())));
      }

      gradeWisePerformanceResponse.add(
          KMData.builder()
              .name(gradeMap.get(data.getGradeSlug()))
              .slug(data.getGradeSlug())
              .percentage(knowledgePercentage)
              .build());
    }
    var totalAttendance = calculateTotalAttendancePercentage(kmSummaryList);
    return KMSummaryResponse.builder()
        .averageKnowledgePercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(totalAttendance.getKnowledgePercentage())))
        .data(gradeWisePerformanceResponse)
        .orgName(organization.getName())
        .build();
  }

  public void createStudentKMeter(Exam exam) {

    if (Objects.isNull(exam)) {
      return;
    }
    List<StudentKMeter> studentKMeter = null;
    if (Constants.PRACTICE_EXAM == exam.getExamType()) {
      studentKMeter =
          studentKMeterRepository.findAllByStudentIdAndSubTopicSlugAndExamType(
              exam.getStudentId(), exam.getSubtopicSlug(), exam.getExamType());

    } else if (Constants.TEST_EXAM == exam.getExamType()) {
      studentKMeter =
          studentKMeterRepository.findAllByStudentIdAndChapterSlugAndExamType(
              exam.getStudentId(), exam.getChapterSlug(), exam.getExamType());

    } else if (Constants.COURSE_TEST == exam.getExamType()
        || Constants.COURSE_ASSIGNMENT == exam.getExamType())
      studentKMeter =
          studentKMeterRepository.findAllByStudentIdAndSubTopicSlugAndExamType(
              exam.getStudentId(), exam.getSubtopicSlug(), exam.getExamType());
    else {
      return;
    }

    if (studentKMeter != null && !studentKMeter.isEmpty()) {
      StudentKMeter studentKMeter1 = studentKMeter.getFirst();
      updateStudentKMeter(studentKMeter1, exam);
    } else {
      saveStudentKMeter(exam);
    }
  }

  private void updateStudentKMeter(StudentKMeter studentKmeter, Exam exam) {
    if (exam.getMarksScored() > 0) {
      studentKmeter.setExamCompletedAt(LocalDateTime.now());
      studentKmeter.setMarksScored(exam.getMarksScored());
      studentKmeter.setKnowledgePercentage(calculateKnowledgePercentage(exam));
      studentKmeter.setExamId(exam.getId());
      studentKMeterRepository.save(studentKmeter);
      log.debug(
          "Updated student knowledge meter for student [ %s ]".formatted(exam.getStudentId()));
    }
  }

  private void saveStudentKMeter(Exam exam) {
    if (Objects.isNull(exam.getMarksScored()) || exam.getMarksScored() == 0) {
      return;
    }
    var studentKmeters =
        StudentKMeter.builder()
            .studentId(exam.getStudentId())
            .examId(exam.getId())
            .examCreatedAt(exam.getCreatedAt().toLocalDateTime())
            .examCompletedAt(LocalDateTime.now())
            .subjectSlug(exam.getSubjectSlug())
            .subjectName(exam.getSubjectName())
            .chapterSlug(exam.getChapterSlug())
            .chapterName(exam.getChapterName())
            .subTopicSlug(exam.getSubtopicSlug())
            .subTopicName(exam.getSubtopicName())
            .examType(exam.getExamType())
            .marksScored(exam.getMarksScored())
            .totalMarks(exam.getTotalMarks())
            .knowledgePercentage(calculateKnowledgePercentage(exam))
            .orgSlug(authService.getStudentDetails().getOrganization())
            .build();
    studentKMeterRepository.save(studentKmeters);
    log.debug("saved student knowledge meter for student [ %s ]".formatted(exam.getStudentId()));
  }

  private Double calculateKnowledgePercentage(Exam exam) {
    return Objects.requireNonNullElse(
        exam.getMarksScored().doubleValue() / exam.getTotalMarks().doubleValue() * 100, 0D);
  }
}
