package com.wexl.retail.msg91.service;

import static com.wexl.retail.msg91.service.Msg91SmsService.MSG91_AUTHKEY_HEADER;

import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.util.Validation;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@RequiredArgsConstructor
@Service
public class Msg91EmailService implements EmailService {

  private final RestTemplate restTemplate;

  @Value("${app.msg91.authkey}")
  private String msgAuthkey;

  @Value("${app.msg91.emailUrl}")
  private String emailUrl;

  @Override
  public void sendEmail(String templateId, List<Msg91Dto.EmailTo> emails) {
    if (StringUtils.isEmpty(templateId) || emails.isEmpty()) {
      log.info("No valid emails to send");
      return;
    }
    var emailRequest = buildEmailRequest(templateId);

    filterValidEmails(emails)
        .forEach(
            recipient -> {
              emailRequest.setRecipients(List.of(buildEmailRecipients(List.of(recipient))));
              try {
                ResponseEntity<Msg91Dto.EmailResponse> response =
                    restTemplate.exchange(
                        emailUrl,
                        HttpMethod.POST,
                        getRequestEntity(emailRequest),
                        Msg91Dto.EmailResponse.class);

                if (!response.getStatusCode().is2xxSuccessful()
                    || Objects.isNull(response.getBody())
                    || response.getBody().isHasError()) {
                  log.error("Error while sending email to msg91 {}", response.getBody());
                } else {
                  log.info("Email sent successfully");
                }
              } catch (Exception e) {
                log.error("Error while sending emails : " + e.getMessage(), e);
              }
            });
  }

  private Msg91Dto.EmailRecipient buildEmailRecipients(List<Msg91Dto.EmailTo> emailTos) {
    return Msg91Dto.EmailRecipient.builder().to(emailTos).build();
  }

  public List<Msg91Dto.EmailTo> filterValidEmails(List<Msg91Dto.EmailTo> emails) {

    var invalidEmails =
        emails.stream().filter(r -> !Validation.isValidEmail(r.getEmail())).toList();
    if (!invalidEmails.isEmpty()) {
      log.info("Invalid emails found :%s".formatted(invalidEmails));
      emails.removeAll(invalidEmails);
    }
    return emails;
  }

  public Msg91Dto.EmailRequest buildEmailRequest(String template) {
    var emailRequest = new Msg91Dto.EmailRequest();
    emailRequest.setFrom(Msg91Dto.EmailFrom.builder().email("<EMAIL>").build());
    emailRequest.setDomain("care.wexledu.com");
    emailRequest.setTemplateId(template);
    return emailRequest;
  }

  private HttpEntity<Msg91Dto.EmailRequest> getRequestEntity(Msg91Dto.EmailRequest request) {
    var headers = new HttpHeaders();
    headers.add(MSG91_AUTHKEY_HEADER, msgAuthkey);
    return new HttpEntity<>(request, headers);
  }
}
