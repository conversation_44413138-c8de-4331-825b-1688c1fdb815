package com.wexl.retail.qrcode.controller;

import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.qrcode.dto.QrCodeDetails;
import com.wexl.retail.qrcode.dto.QrCodeGenRequest;
import com.wexl.retail.qrcode.dto.SignupRequest;
import com.wexl.retail.qrcode.dto.StudentSignInDetails;
import com.wexl.retail.qrcode.service.QrCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping
public class QrCodeController {

  private final QrCodeService qrCodeService;

  @GetMapping("/public/qr-link/{code}")
  public ResponseEntity<String> qrLink(@PathVariable String code) {
    HttpHeaders headers = new HttpHeaders();
    if ("32Yq3lA".equals(code)) {
      headers.add(HttpHeaders.LOCATION, "https://maps.app.goo.gl/1fYv14UmmZcLN9WP6?g_st=iw");
      return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }
    headers.add(HttpHeaders.LOCATION, "https://maps.app.goo.gl/17ZsBo372vEHdr52A?g_st=ia");
    return new ResponseEntity<>(headers, HttpStatus.FOUND);
  }

  @GetMapping("/student/qr-signup")
  public ResponseEntity<QrCodeDetails> getByUuid(@RequestParam String uuid) {
    return new ResponseEntity<>(qrCodeService.getStudentDetails(uuid), HttpStatus.OK);
  }

  @PostMapping("/student/qr-signup")
  public StudentSignInDetails signUpUsingUuid(@RequestBody SignupRequest signupRequest) {
    return qrCodeService.createStudent(signupRequest);
  }

  @PostMapping("/bulk-create:qr-codes")
  public S3FileUploadResult genQrCode(@RequestBody QrCodeGenRequest qrCodeGenRequest) {
    return qrCodeService.genQrCode(qrCodeGenRequest);
  }
}
