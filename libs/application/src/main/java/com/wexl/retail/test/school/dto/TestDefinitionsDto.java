package com.wexl.retail.test.school.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record TestDefinitionsDto() {
  public record EditTestDefinitionRequest(String instruction, String name) {}

  @Builder
  public record TestDefinitionResponse(
      @JsonProperty("test_definition_id") Long testDefId,
      @JsonProperty("test_type") String testType,
      @JsonProperty("test_definition_name") String name,
      @JsonProperty("isQpGenPresent") boolean isQpGenPresent,
      List<Sections> sectionsList,
      String instructions,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("asset_slug") String assetSlug,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      String message) {}

  @Builder
  public record Sections(
      @JsonProperty("id") Long id,
      @JsonProperty("section_name") String name,
      @JsonProperty("seq_num") Long sequenceNumber,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      @JsonProperty("no_of_questions_saved") Long noOfQuestionsSaved,
      @JsonProperty("instructions") String instructions,
      @JsonProperty("blueprint_section_id") Long blueprintSectionId,
      @JsonProperty("blueprint_id") Long blueprintId,
      List<QuestionDto.Question> questions,
      @JsonProperty("question_tag") String questionTag) {}

  @Builder
  public record TestDefinitionSectionRequest(
      @JsonProperty("section_name") String name,
      @JsonProperty("no_of_questions") Long noOfQuestions) {}

  @Builder
  public record SectionsResponse(
      @JsonProperty("id") Long id,
      @JsonProperty("section_name") String name,
      @JsonProperty("seq_num") Long sequenceNumber,
      @JsonProperty("no_of_questions") Long noOfQuestions,
      @JsonProperty("total_marks") Long totalMarks,
      @JsonProperty("marks_scored") Float marksScored,
      @JsonProperty("ielts_score") Double ieltsScored,
      @JsonProperty("level") String level,
      @JsonProperty("score") String score) {}

  @Builder
  public record ZeroDigitalQuestionRequest(
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("chapter_slugs") List<String> chapterSlugs) {}

  @Builder
  public record TestEnrichmentRequest(
      String board,
      String grade,
      String subject,
      String question,
      String subtopic,
      String chapter) {}

  @Builder
  public record UploadQuestionRequest(String board, String grade, String subject) {}
}
