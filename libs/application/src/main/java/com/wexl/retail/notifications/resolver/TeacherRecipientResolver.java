package com.wexl.retail.notifications.resolver;

import com.wexl.retail.notifications.dto.EmailRecipientGroup;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.repository.TeacherRepository;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TeacherRecipientResolver implements RecipientResolver {

  @Autowired private TeacherRepository teacherRepository;

  @Override
  public boolean supports(NotificationDto.SendTo sendTo) {
    return sendTo.groups() != null && sendTo.groups().contains(EmailRecipientGroup.TEACHER.name())
        || (sendTo.individuals() != null
            && sendTo.individuals().containsKey(EmailRecipientGroup.TEACHER));
  }

  @Override
  public List<Object> resolveRecipients(String orgSlug, NotificationDto.SendTo sendTo) {
    List<Object> teachers = new ArrayList<>();
    if (sendTo.groups() != null && sendTo.groups().contains(EmailRecipientGroup.TEACHER.name())) {
      teachers.addAll(teacherRepository.getAllTeachersByOrg(orgSlug));
    }
    if (sendTo.individuals() != null
        && sendTo.individuals().containsKey(EmailRecipientGroup.TEACHER)) {
      List<Long> teacherIds = sendTo.individuals().get(EmailRecipientGroup.TEACHER);
      teachers.addAll(teacherRepository.findAllById(teacherIds));
    }
    return teachers;
  }
}
