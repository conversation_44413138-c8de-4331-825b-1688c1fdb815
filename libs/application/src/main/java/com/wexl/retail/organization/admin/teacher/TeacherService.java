package com.wexl.retail.organization.admin.teacher;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.dac.knowledgemeter.service.DacKmService;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.globalprofile.service.RoleTemplateService;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.reportcards.WeeklyReportService;
import com.wexl.retail.mlp.dto.KMGradesRequest;
import com.wexl.retail.mobile.model.CountryCode;
import com.wexl.retail.mobile.repository.CountryCodeRepository;
import com.wexl.retail.model.*;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.organization.admin.teacher.TeacherDto.UpdateExternalRefRequest;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.student.auth.StudentAuthTransformer;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.teacher.auth.TeacherAuthService;
import com.wexl.retail.teacher.auth.TeacherSignupRequest;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.teacher.teacherpublisher.TeacherCreationEventPublisher;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.dto.ScheduleTestDto;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeacherService {

  private final TeacherRepository teacherRepository;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final UserRepository userRepository;
  private final TeacherCreationEventPublisher teacherCreationEventPublisher;
  private final GuardianService guardianService;
  private final TeacherAuthService teacherAuthService;
  private final AuthService authService;
  private final WeeklyReportService weeklyReportService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final RoleTemplateService roleTemplateService;
  private final UserRoleHelper userRoleHelper;
  private final OrganizationRepository organizationRepository;
  private final StrapiService strapiService;
  private final TeacherOrgsService teacherOrgsService;
  private final CountryCodeRepository countryCodeRepository;
  private final SectionRepository sectionRepository;
  private final TeacherSectionRepository teacherSectionRepository;
  private final StudentAuthTransformer studentAuthTransformer;
  private final DateTimeUtil dateTimeUtil;
  private final DacKmService dacKmService;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;

  public void createTeacher(String orgId, TeacherRequest teacherRequest) {
    if (!isValidPassword(teacherRequest.getPassword())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid password");
    }

    var userEmailExist = authService.isUserEmailExist(teacherRequest.getEmail());
    if (!userEmailExist) {
      var teacherSignupRequest = teacherSignupRequestFrom(orgId, teacherRequest);
      teacherAuthService.createTeacherInBulk(teacherSignupRequest);

      if (teacherRequest.getTeacherMetadata() != null) {
        setTeacherMetadata(
            teacherRequest.getTeacherMetadata(), teacherSignupRequest.getEmailAddress());
      }
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Teacher email already exists");
    }
  }

  private void setTeacherMetadata(TeacherMetadata teacherMetadata, String email) {

    var user = userRepository.findUserByEmail(email);

    if (user.isEmpty()) {
      return;
    }

    var optionalTeacher = teacherRepository.findByUserInfo(user.get());

    if (optionalTeacher.isPresent()) {
      var teacher = optionalTeacher.get();

      teacher.setMetadata(teacherMetadata);

      teacherRepository.save(teacher);
    }
  }

  public List<TeacherDto.TeacherResponseWithMetrics> getAllTeacherDetailsWithMetrics(String orgId) {
    try {
      List<OrgTeacher> orgTeachers = teacherRepository.findAllTeachers(orgId);

      List<OrgTeacher> teachers = getUniqueTeachers(orgTeachers);
      return teachers.stream()
          .sorted(Comparator.comparing(OrgTeacher::getTeacherId).reversed())
          .map(this::getTeacherResponse)
          .toList();
    } catch (Exception e) {
      log.error("Failed to get teachers " + e.getMessage(), e);
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Failed to get teachers " + e.getMessage());
    }
  }

  private TeacherDto.TeacherResponseWithMetrics getTeacherResponse(OrgTeacher orgTeacher) {
    var teacherSubjects = teacherSubjectsRepository.findByTeacher(orgTeacher.getAuthUserId());
    var allSubjects = strapiService.getAllSubjects();
    Map<String, String> slugToNameMap =
        allSubjects.stream().collect(Collectors.toMap(Entity::getSlug, Entity::getName));
    Map<String, TeacherDto.TeacherResponseWithMetrics.Board> boardMap = new HashMap<>();
    for (var ts : teacherSubjects) {
      var section = ts.getSection();
      var boardName = section.getBoardName();
      var gradeSlug = section.getGradeSlug();
      var sectionName = section.getName();
      var subjectSlug = ts.getSubject();
      var subjectName = slugToNameMap.get(subjectSlug);
      TeacherDto.TeacherResponseWithMetrics.Board board = boardMap.get(boardName);
      if (board == null) {
        board = new TeacherDto.TeacherResponseWithMetrics.Board(boardName, new ArrayList<>());
        boardMap.put(boardName, board);
      }
      TeacherDto.TeacherResponseWithMetrics.Grade grade = findGrade(board, gradeSlug);
      TeacherDto.TeacherResponseWithMetrics.SectionWithSubjects sectionResponse =
          findSection(grade, sectionName);

      if (subjectName != null && !sectionResponse.subjects().contains(subjectName)) {
        sectionResponse.subjects().add(subjectName);
      }
    }
    List<TeacherDto.TeacherResponseWithMetrics.Board> boards = new ArrayList<>(boardMap.values());
    Long lastLoginTime = null;
    if (orgTeacher.getLastLoginTime() != null) {
      lastLoginTime =
          dateTimeUtil.convertTimeStampToLong((Timestamp) orgTeacher.getLastLoginTime());
    }
    return TeacherDto.TeacherResponseWithMetrics.builder()
        .email(orgTeacher.getEmail())
        .firstName(orgTeacher.getFirstName())
        .lastName(orgTeacher.getLastName())
        .mobileNumber(orgTeacher.getMobileNumber())
        .orgAdmin(AppTemplate.ADMIN.toString().equals(orgTeacher.getAdmin()))
        .subjectPreferences(
            orgTeacher.getSubjects() == null ? new ArrayList<>() : orgTeacher.getSubjects())
        .boards(boards)
        .lastLoginTime(lastLoginTime)
        .build();
  }

  private TeacherDto.TeacherResponseWithMetrics.Grade findGrade(
      TeacherDto.TeacherResponseWithMetrics.Board board, String gradeSlug) {
    for (var grade : board.grades()) {
      if (grade.gradeSlug().equals(gradeSlug)) {
        return grade;
      }
    }
    var newGrade = new TeacherDto.TeacherResponseWithMetrics.Grade(gradeSlug, new ArrayList<>());
    board.grades().add(newGrade);
    return newGrade;
  }

  private TeacherDto.TeacherResponseWithMetrics.SectionWithSubjects findSection(
      TeacherDto.TeacherResponseWithMetrics.Grade grade, String sectionName) {
    for (var section : grade.sections()) {
      if (section.sectionName().equals(sectionName)) {
        return section;
      }
    }
    var newSection =
        new TeacherDto.TeacherResponseWithMetrics.SectionWithSubjects(
            sectionName, new ArrayList<>());
    grade.sections().add(newSection);
    return newSection;
  }

  public List<TeacherResponse> getTeachers(String orgId) {
    try {
      List<OrgTeacher> orgTeachers = teacherRepository.findAllTeachers(orgId);

      List<OrgTeacher> teachers = getUniqueTeachers(orgTeachers);
      return teachers.stream()
          .sorted(Comparator.comparing(OrgTeacher::getTeacherId).reversed())
          .map((this::getTeacherResponseFrom))
          .toList();
    } catch (Exception e) {
      log.error("Failed to get teachers " + e.getMessage(), e);
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Failed to get teachers " + e.getMessage());
    }
  }

  public TeacherResponse getTeacher(String orgId, String teacherId) {
    var teacher = getTeacherFor(orgId, teacherId);

    return getResponse(teacher);
  }

  public TeacherResponse editTeacher(
      String orgId, String teacherId, TeacherRequest teacherRequest) {
    var teacher = getTeacherFor(orgId, teacherId);
    var teacherUser = teacher.getUserInfo();
    mapAdminSchedulesToAnotherAdmin(teacherUser);
    var roleTemplate =
        roleTemplateService.getRoleTemplateById(teacherRequest.getRoleTemplate().getId());
    teacherUser.setFirstName(teacherRequest.getFirstName());
    teacherUser.setLastName(teacherRequest.getLastName());
    teacherUser.setMobileNumber(teacherRequest.getMobileNumber());
    teacherUser.setCountryCode(teacherRequest.getCountryCode());
    updatePassword(teacherId, teacherRequest.getPassword());
    teacher.setUserInfo(teacherUser);
    teacher.setRoleTemplate(roleTemplate);

    if (teacherRequest.getTeacherMetadata() != null) {
      teacher.setMetadata(teacherRequest.getTeacherMetadata());
    }

    var updatedTeacher = teacherRepository.save(teacher);
    teacherCreationEventPublisher.teacherCreationEvent(updatedTeacher);
    return getResponse(updatedTeacher);
  }

  private void mapAdminSchedulesToAnotherAdmin(User userTeacher) {
    if (userRoleHelper.isOrgAdmin(userTeacher)) {
      var teacherSchedules =
          offlineTestDefinitionRepository.findAllByTeacherId(userTeacher.getTeacherInfo().getId());
      if (!teacherSchedules.isEmpty()) {
        var userAdmin = authService.getTeacherDetails();
        teacherSchedules.forEach(
            schedule -> schedule.setTeacherId(userAdmin.getTeacherInfo().getId()));
        offlineTestDefinitionRepository.saveAll(teacherSchedules);
      }
    }
  }

  public void deleteTeacher(String orgId, String teacherId) {
    var teacher = getTeacherFor(orgId, teacherId);
    removeAsClassTeacherForSection(teacher);
    mapAdminSchedulesToAnotherAdmin(teacher.getUserInfo());
    teacherAuthService.deleteTeacher(teacher.getUserInfo());
  }

  private void removeAsClassTeacherForSection(Teacher teacher) {
    var section =
        teacher.getSections().stream()
            .filter(sec -> sec.getClassTeacher() != (null) && sec.getClassTeacher().equals(teacher))
            .toList();
    if (!section.isEmpty()) {
      var sec = section.getFirst();
      sec.setClassTeacher(null);
      sectionRepository.save(sec);
    }
  }

  private void updatePassword(String teacherId, String password) {
    if (nonNull(password) && isValidPassword(password)) {
      teacherAuthService.updatePassword(teacherId, password);
    } else if (nonNull(password)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid password");
    }
  }

  private Teacher getTeacherFor(String orgId, String teacherId) {
    var teacherUser = userRepository.getUserByAuthUserId(teacherId);

    if (isNull(teacherUser) || !orgId.equals(teacherUser.getOrganization())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Teacher not found");
    }

    Optional<Teacher> optionalTeacher = teacherRepository.findByUserInfo(teacherUser);
    if (optionalTeacher.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Teacher not found");
    }

    return optionalTeacher.get();
  }

  private List<OrgTeacher> getUniqueTeachers(List<OrgTeacher> orgTeachers) {
    Map<String, OrgTeacher> teacherMap = new HashMap<>();
    orgTeachers.forEach(teacher -> teacherMap.put(teacher.getAuthUserId(), teacher));
    return new ArrayList<>(teacherMap.values());
  }

  private TeacherResponse getResponse(Teacher teacher) {
    final List<UserRole> userRoles = userRoleHelper.getUserRolesFromUser(teacher.getUserInfo());
    return TeacherResponse.builder()
        .email(teacher.getUserInfo().getEmail())
        .userName(teacher.getUserInfo().getUserName())
        .firstName(teacher.getUserInfo().getFirstName())
        .lastName(teacher.getUserInfo().getLastName())
        .mobileNumber(teacher.getUserInfo().getMobileNumber())
        .authId(teacher.getUserInfo().getAuthUserId())
        .teacherId(teacher.getId())
        .orgAdmin(hasAdminRole(userRoles))
        .subjects(teacher.getMetadata().getSubjects())
        .countryResponse(
            buildCountryResponse(
                countryCodeRepository
                    .findByCode(teacher.getUserInfo().getCountryCode())
                    .orElse(null)))
        .userRoles(userRoles)
        .roleTemplates(
            teacher.getRoleTemplate() == null
                ? null
                : roleTemplateService.getRoleTemplates(teacher.getRoleTemplate()))
        .build();
  }

  public TeacherDto.CountryResponse buildCountryResponse(CountryCode countryCode) {
    if (Objects.isNull(countryCode)) {
      return null;
    }
    return TeacherDto.CountryResponse.builder()
        .code(countryCode.getCode())
        .name(countryCode.getName())
        .id(countryCode.getId())
        .build();
  }

  private boolean hasAdminRole(List<UserRole> userRoles) {
    return userRoles.stream().anyMatch(UserRole.ROLE_ORG_ADMIN::equals);
  }

  private TeacherSignupRequest teacherSignupRequestFrom(
      String orgId, TeacherRequest teacherRequest) {
    var roleTemplate =
        roleTemplateService.getRoleTemplateById(teacherRequest.getRoleTemplate().getId());
    return TeacherSignupRequest.builder()
        .userName(teacherRequest.getUserName())
        .firstName(teacherRequest.getFirstName())
        .lastName(teacherRequest.getLastName())
        .mobileNumber(teacherRequest.getMobileNumber())
        .countryCode(teacherRequest.getCountryCode())
        .externalRef(teacherRequest.getExternalRef())
        .emailAddress(teacherRequest.getEmail())
        .orgSlug(orgId)
        .password(teacherRequest.getPassword())
        .hearAboutFrom("OTHER")
        .termsAndConditions(true)
        .orgAdmin(teacherRequest.isOrgAdmin())
        .roleType(teacherRequest.getRoleType())
        .roleTemplate(roleTemplate)
        .build();
  }

  private boolean isValidPassword(String password) {
    return password != null && password.length() >= 1;
  }

  private TeacherResponse getTeacherResponseFrom(OrgTeacher orgTeacher) {
    var allSubjects = strapiService.getAllSubjects();
    Map<String, String> slugToNameMap =
        allSubjects.stream().collect(Collectors.toMap(Entity::getSlug, Entity::getName));

    List<TeacherDto.Subject> subjectList = new ArrayList<>();
    List<String> subjects =
        orgTeacher.getSubjects().stream()
            .flatMap(subject -> Arrays.stream(subject.split(",")))
            .map(String::trim)
            .distinct()
            .collect(Collectors.toList());
    subjects.forEach(
        subject -> {
          subjectList.add(
              TeacherDto.Subject.builder().slug(subject).name(slugToNameMap.get(subject)).build());
        });
    return TeacherResponse.builder()
        .email(orgTeacher.getEmail())
        .userName(orgTeacher.getUsername())
        .firstName(orgTeacher.getFirstName())
        .lastName(orgTeacher.getLastName())
        .mobileNumber(orgTeacher.getMobileNumber())
        .authId(orgTeacher.getAuthUserId())
        .teacherId(orgTeacher.getTeacherId())
        .orgAdmin(AppTemplate.ADMIN.toString().equals(orgTeacher.getAdmin()))
        .organization(orgTeacher.getOrgName())
        .subjects(orgTeacher.getSubjects())
        .subjectList(subjectList)
        .lastLoginTime(studentAuthTransformer.getLastLogin(orgTeacher.getLastLoginTime()))
        .build();
  }

  public Teacher getTeacherByAuthId(String teacherAuthId) {
    User user =
        userRepository
            .findByAuthUserId(teacherAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));

    return teacherRepository
        .findByUserInfo(user)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));
  }

  public List<GenericMetricResponse> getDacReport(String orgSlug) {
    User user = authService.getUserDetails();
    List<Organization> teacherOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    List<String> teacherOrgSlugs = teacherOrgs.stream().map(Organization::getSlug).toList();
    List<String> orgSlugs =
        teacherOrgSlugs.stream()
            .filter(slug -> organizationRepository.getAllChildOrgSlugs(orgSlug).contains(slug))
            .toList();
    return buildDacResponse(
        scheduleTestRepository.findByOrgSlugInAndIsDacIsTrue(orgSlugs), orgSlugs);
  }

  private List<GenericMetricResponse> buildDacResponse(
      List<ScheduleTest> results, List<String> orgSlug) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    orgSlug.forEach(
        org -> {
          var count = results.stream().filter(x -> x.getOrgSlug().equals(org)).distinct().count();
          var organization = organizationRepository.findBySlug(org);
          responseList.add(
              GenericMetricResponse.builder()
                  .data(
                      Map.of(
                          "institute_name",
                          organization.getName(),
                          "org_slug",
                          organization.getSlug(),
                          "count",
                          count))
                  .build());
        });
    return responseList;
  }

  public List<GenericMetricResponse> getOfflineAndOnlineTestReport(String orgSlug) {
    var testSchedules = scheduleTestRepository.findByOrgSlugAndMetadataIsNotNull(orgSlug);
    return buildOfflineAndOnlineTestReport(testSchedules, orgSlug);
  }

  private List<GenericMetricResponse> buildOfflineAndOnlineTestReport(
      List<ScheduleTest> scheduleTests, String orgSlug) {
    Map<String, Object> data = new HashMap<>();
    Map<String, Object> summary = new HashMap<>();
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var strapiGrades = strapiService.getAllGrades();
    var organization = organizationRepository.findBySlug(orgSlug);
    summary.put("organization_name", organization.getName());
    var grades = scheduleTests.stream().map(x -> x.getMetadata().getGrade()).distinct().toList();
    List<ScheduleTestDto.OnlineTestAndOfflineTestCounts> testCounts = new ArrayList<>();
    var finalGrades = strapiGrades.stream().filter(g -> grades.contains(g.getSlug())).toList();
    finalGrades.forEach(
        grade ->
            testCounts.add(
                ScheduleTestDto.OnlineTestAndOfflineTestCounts.builder()
                    .grade(grade.getSlug())
                    .gradeName(grade.getName())
                    .offlineTestCount(buildOfflineTestCount(grade.getSlug(), scheduleTests))
                    .onlineTestCount(buildOnlineTestCount(grade.getSlug(), scheduleTests))
                    .build()));
    data.put("test_details", testCounts);
    responseList.add(GenericMetricResponse.builder().summary(summary).data(data).build());
    return responseList;
  }

  private Long buildOnlineTestCount(String grade, List<ScheduleTest> scheduleTests) {
    return scheduleTests.stream()
        .filter(
            x ->
                x.getMetadata().getGrade().equals(grade)
                    && Boolean.FALSE.equals(x.getIsDac())
                    && x.getTestDefinition().getType().equals(TestType.MOCK_TEST))
        .count();
  }

  private Long buildOfflineTestCount(String grade, List<ScheduleTest> scheduleTests) {
    return scheduleTests.stream()
        .filter(x -> x.getMetadata().getGrade().equals(grade) && Boolean.TRUE.equals(x.getIsDac()))
        .count();
  }

  public List<GenericMetricResponse> getOnlineTestReportByGrade(String orgSlug, String gradeSlug) {
    var scheduleTests = scheduleTestRepository.findByOrgSlugAndMetadataIsNotNull(orgSlug);
    var onlineTests =
        scheduleTests.stream()
            .filter(
                x ->
                    x.getMetadata().getGrade() != null
                        && x.getMetadata().getGrade().equals(gradeSlug)
                        && Boolean.FALSE.equals(x.getIsDac())
                        && x.getTestDefinition().getType().equals(TestType.MOCK_TEST))
            .toList();
    return buildTestReportByGrade(onlineTests, true);
  }

  private List<GenericMetricResponse> buildTestReportByGrade(
      List<ScheduleTest> tests, Boolean isOnlineTest) {
    Map<String, Object> data = new HashMap<>();
    List<GenericMetricResponse> responseList = new ArrayList<>();
    List<ScheduleTestDto.OnlineTestCountsByGrade> testData = new ArrayList<>();
    tests.forEach(
        test ->
            testData.add(
                ScheduleTestDto.OnlineTestCountsByGrade.builder()
                    .attemptedCount(
                        test.getScheduleTestStudent().stream()
                            .filter(x -> x.getStatus().equals(TaskStatus.COMPLETED.toString()))
                            .count())
                    .notAttemptedCount(
                        test.getScheduleTestStudent().stream()
                            .filter(x -> x.getStatus().equals(TaskStatus.PENDING.toString()))
                            .count())
                    .name(test.getTestDefinition().getTestName())
                    .testScheduleId(test.getId())
                    .build()));
    data.put(
        Boolean.TRUE.equals(isOnlineTest) ? "online_test_counts" : "offline_test_counts", testData);
    responseList.add(GenericMetricResponse.builder().data(data).build());
    return responseList;
  }

  public List<GenericMetricResponse> getOfflineTestReportByGrade(String orgSlug, String gradeSlug) {
    var scheduleTests = scheduleTestRepository.findByOrgSlugAndMetadataIsNotNull(orgSlug);
    var onlineTests =
        scheduleTests.stream()
            .filter(
                x ->
                    x.getMetadata().getGrade() != null
                        && x.getMetadata().getGrade().equals(gradeSlug)
                        && Boolean.TRUE.equals(x.getIsDac()))
            .toList();
    return buildTestReportByGrade(onlineTests, false);
  }

  public List<TeacherResponse> getChildOrgTeachersDetails(List<Organization> childOrgs) {
    List<TeacherResponse> teacherResponses = new ArrayList<>();
    for (Organization org : childOrgs) {
      teacherResponses.addAll(getTeachers(org.getSlug()));
    }
    return teacherResponses;
  }

  public void updateExternalRef(UpdateExternalRefRequest updateExternalRefRequest) {
    var possibleUser =
        userRepository.findByEmailAndOrganization(
            updateExternalRefRequest.email(), updateExternalRefRequest.orgSlug());
    if (possibleUser.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "User not found");
    }

    final User user = possibleUser.get();
    var teacher = teacherRepository.findByUserInfo(user);
    if (teacher.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Teacher not found");
    }

    user.setExternalRef(updateExternalRefRequest.externalRef());
    userRepository.save(user);
  }

  public List<GenericMetricResponse> getDacKmReport(String orgSlug, String board) {
    User user = authService.getUserDetails();
    List<Organization> teacherOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    List<String> teacherOrgSlugs = teacherOrgs.stream().map(Organization::getSlug).toList();
    List<String> orgSlugs =
        teacherOrgSlugs.stream()
            .filter(slug -> organizationRepository.getAllChildOrgSlugs(orgSlug).contains(slug))
            .toList();
    return buildDacKmResponse(orgSlugs, board);
  }

  private List<GenericMetricResponse> buildDacKmResponse(List<String> orgSlugs, String board) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    List<KMGradesRequest> gradeRequest = new ArrayList<>();
    orgSlugs.forEach(
        orgSlug -> {
          var organization = organizationRepository.findBySlug(orgSlug);
          var dacKm = dacKmService.getSummaryByBoard(orgSlug, board, null, null, gradeRequest);
          responseList.add(
              GenericMetricResponse.builder()
                  .data(
                      Map.of(
                          "institute_name",
                          organization.getName(),
                          "org_slug",
                          organization.getSlug(),
                          "knowledge_percentage",
                          dacKm.averagePercentage() == null ? 0.0 : dacKm.averagePercentage()))
                  .build());
        });
    return responseList;
  }

  public Teacher validateTeacher(Long teacherId) {
    return teacherRepository
        .findById(teacherId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));
  }
}
