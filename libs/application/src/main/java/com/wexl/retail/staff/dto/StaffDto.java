package com.wexl.retail.staff.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.model.Gender;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record StaffDto() {

  @Builder
  public record StaffRequest(
      @NotNull String firstName,
      String lastName,
      @NotNull String email,
      @NotNull String password,
      String mobileNumber,
      String address,
      List<Long> role,
      @JsonProperty("designation_id") Long designationId,
      @JsonProperty("department_id") Long departmentId,
      Gender gender,
      @JsonProperty("joining_date") String joiningDate) {}

  @Builder
  public record StaffResponse(
      Long id,
      String firstName,
      String lastName,
      String email,
      String mobileNumber,
      String address,
      List<GlobalProfileDto.RoleTemplateResponse> roles,
      DesignationResponse designation,
      DepartmentResponse department,
      String gender,
      String joiningDate,
      UserResponse user) {}

  @Builder
  public record UserResponse(Long id, String userName, String authUserId) {}

  @Builder
  public record DesignationRequest(
      @NotNull String name,
      String code,
      String description,
      @JsonProperty("department_id") Long departmentId,
      Boolean active) {}

  @Builder
  public record DesignationResponse(
      Long id,
      String name,
      String code,
      String description,
      DepartmentResponse department,
      Boolean active) {}

  @Builder
  public record DepartmentRequest(
      @NotNull String name, String code, String description, Boolean active) {}

  @Builder
  public record DepartmentResponse(
      Long id, String name, String code, String description, Boolean active) {}

  @Builder
  public record StaffListResponse(List<StaffResponse> staffList) {}
}
