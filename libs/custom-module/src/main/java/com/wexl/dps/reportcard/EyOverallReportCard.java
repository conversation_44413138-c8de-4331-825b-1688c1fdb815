package com.wexl.dps.reportcard;

import com.wexl.dps.assesmentobjectives.service.AssessmentObjectiveService;
import com.wexl.dps.learningmilestones.service.LmrStudentService;
import com.wexl.dps.managereportcard.dto.EyReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.dps.preprimary.repository.DpsPreprimaryAprRepository;
import com.wexl.dps.preprimary.service.PrePrimaryAprService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudentAttendance;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class EyOverallReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final StudentService studentService;
  private final UserService userService;
  private final TermRepository termRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final DpsPreprimaryAprRepository dpsPreprimaryAprRepository;
  private final PrePrimaryAprService prePrimaryAprService;
  private final LmrStudentService lmrStudentService;
  private final AssessmentObjectiveService assessmentObjectiveService;
  private static final String FALSE = "false";
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request, org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    if (Objects.equals(reportCardTemplate.getConfig(), "dps-ey-1st-12th-ia1-report-card.xml")) {
      return config.equals("dps-ey-1st-12th-ia1-report-card.xml");
    }
    return config.equals("dps-ey-1st-12th-report-card.xml");
  }

  private EyReportDto.Body buildBody(
      User user, ReportCardDto.Request request, Organization organization) {
    var student = studentService.findByUserInfo(user);
    var classTeacher = student.getSection().getClassTeacher();
    var tableMarks = buildTableMarks(request.termId(), student);
    String gradeFacilitatorName =
        (classTeacher != null && classTeacher.getUserInfo() != null)
            ? userService.getNameByUserInfo(classTeacher.getUserInfo())
            : "";
    String schoolSection =
        switch (student.getSection().getGradeSlug()) {
          case "i", "ii", "iii", "iv", "v" -> "Primary School Section";
          case "vi", "vii", "viii" -> "Middle School Section";
          case "ix", "x" -> "High School Section";
          case "xi", "xii", "xig", "xiig" -> "Cambridge Advanced";
          default -> null;
        };

    String cambridgeCurriculum =
        switch (student.getSection().getGradeSlug()) {
          case "i", "ii", "iii", "iv", "v" -> "CAMBRIDGE PRIMARY CHECKPOINT CURRICULUM";
          case "vi", "vii", "viii" -> "CAMBRIDGE LOWER SECONDARY CHECKPOINT CURRICULUM";
          case "ix", "x" -> "CAMBRIDGE IGCSE CURRICULUM";
          case "xi", "xii", "xig", "xiig" -> " AS/A LEVEL";
          default -> null;
        };

    var term = termRepository.findById(request.termId());
    String assessmentName =
        switch (term.get().getSlug()) {
          case "t1" -> "mid term assessment :";
          case "t2" -> "end term assessment :";
          case "ia1" -> "internal assessment 1 :";
          case "ia2" -> "internal assessment 2 :";
          case "mock1" -> "mock assessment 1 :";
          case "mock2" -> "mock assessment 2 :";
          default -> "mid term assessment :";
        };
    try {
      var offlineTestDefinationIds =
          tableMarks.firstTableMarks().stream().map(EyReportDto.Marks::otdId).distinct().toList();
      List<OfflineTestScheduleStudentAttendance> studentDataList = new ArrayList<>();
      if (!offlineTestDefinationIds.isEmpty()) {
        for (var ids : offlineTestDefinationIds) {
          var testDefination = offlineTestScheduleService.validateOfflineTestDefinition(ids);
          var studentData =
              offlineTestScheduleStudentAttendanceRepository
                  .findByOfflineTestDefinitionAndStudentId(testDefination, student.getId());
          if (studentData != null) {
            studentDataList.add(studentData);
          }
        }
      }
      Optional<OfflineTestScheduleStudentAttendance> studentAttendance =
          studentDataList.isEmpty()
              ? Optional.empty()
              : studentDataList.stream()
                  .max(Comparator.comparing(OfflineTestScheduleStudentAttendance::getUpdatedAt))
                  .stream()
                  .findAny();

      return EyReportDto.Body.builder()
          .schoolSectionName(Objects.nonNull(schoolSection) ? schoolSection.toUpperCase() : null)
          .grade(student.getSection().getName())
          .centreNo(student.getSection().getGradeSlug())
          .dateId("")
          .orgSlug(organization.getSlug())
          .attendance(getAttendance(studentAttendance))
          .assessment(
              tableMarks.firstTableMarks().getFirst() != null
                  ? tableMarks.firstTableMarks().getFirst().startDate()
                  : null)
          .assessmentName(assessmentName)
          .termId(String.valueOf(request.termId()))
          .cambridgeCurriculum(cambridgeCurriculum)
          .gradeFacilitatorName(gradeFacilitatorName)
          .gradeFacilitatorComments(
              studentAttendance.map(OfflineTestScheduleStudentAttendance::getRemarks).orElse(""))
          .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
          .firstTable(
              buildFirstTable(
                  tableMarks.firstTableMarks(),
                  student,
                  request.templateId(),
                  term.get().getSlug()))
          .secondReportTables(
              lmrStudentService.getEyLmrReportForOverall(
                  student,
                  Objects.isNull(request.offlineTestDefinitionId())
                      ? buildNewRequest(request, student, term.get().getSlug())
                      : request))
          .aoReport(
              mapToEyAoReport(
                  buildFirstTable(
                      tableMarks.firstTableMarks(),
                      student,
                      request.templateId(),
                      term.get().getSlug()),
                  buildAssessment(student, request.termId())))
          .build();
    } catch (Exception e) {
      log.error("Unable to download Report Card", e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReportCardError");
    }
  }

  private ReportCardDto.Request buildNewRequest(
      ReportCardDto.Request request, Student student, String termSlug) {
    if (request.templateId() == null) {
      return request;
    }
    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlugAndTemplateId(
            student.getUserInfo().getOrganization(),
            student.getSection().getBoardSlug(),
            student.getSection().getGradeSlug(),
            request.templateId());
    var reportCardConfig =
        reportCardConfigs.stream()
            .filter(
                rc ->
                    rc.getReportCardConfigDetails().stream()
                        .anyMatch(
                            rccd -> rccd.getTermAssessment().getTerm().getSlug().equals(termSlug)))
            .findFirst();
    if (reportCardConfig.isPresent() && reportCardConfig.get().getSubjectMetadataId() != null) {
      return ReportCardDto.Request.builder()
          .sectionUuid(request.sectionUuid())
          .templateId(request.templateId())
          .childOrg(request.childOrg())
          .withMarks(request.withMarks())
          .offlineTestDefinitionId(reportCardConfig.get().getSubjectMetadataId())
          .studentAuthId(request.studentAuthId())
          .termId(request.termId())
          .build();
    }
    return request;
  }

  private String getAttendance(Optional<OfflineTestScheduleStudentAttendance> studentAttendance) {
    if (studentAttendance.isEmpty() || Objects.isNull(studentAttendance.get().getPresentDays())) {
      return "NA";
    }
    return studentAttendance
        .map(
            attendance ->
                attendance.getPresentDays() == 0
                    ? "NA"
                    : attendance.getPresentDays()
                        + " / "
                        + (attendance.getOfflineTestDefinition() != null
                            ? attendance.getOfflineTestDefinition().getTotalAttendanceDays()
                            : null))
        .orElse(null);
  }

  public ReportCardConfigDto.AoReport buildAssessment(Student student, Long termId) {
    return assessmentObjectiveService.getEyAssessmentReportsWithTermId(student, termId, null);
  }

  private EyReportDto.AoReport mapToEyAoReport(
      EyReportDto.FirstPageFirstTable firstPageFirstTable, ReportCardConfigDto.AoReport aoReport) {
    List<EyReportDto.GradeDetails> updatedAoTables = new ArrayList<>();
    if (aoReport == null) {
      return null;
    }
    for (var aoTable : aoReport.aoTables()) {
      var matchingFirstPage = findMatchingSubject(aoTable, firstPageFirstTable);

      EyReportDto.GradeDetails newAoTable;
      if (matchingFirstPage.isPresent()) {
        var firstPage = matchingFirstPage.get();
        newAoTable =
            EyReportDto.GradeDetails.builder()
                .grade(firstPage.grade())
                .name(firstPage.subject())
                .build();
      } else {
        newAoTable =
            EyReportDto.GradeDetails.builder()
                .grade(null)
                .name(aoTable.title().toUpperCase())
                .build();
      }

      updatedAoTables.add(newAoTable);
    }

    return new EyReportDto.AoReport(aoReport, updatedAoTables);
  }

  private Optional<EyReportDto.Marks> findMatchingSubject(
      ReportCardConfigDto.AoTable aoTable, EyReportDto.FirstPageFirstTable firstPageFirstTable) {
    try {
      return firstPageFirstTable.marks().stream()
          .filter(firstPage -> Objects.equals(firstPage.subjectSlug(), aoTable.subjectSlug()))
          .findAny();
    } catch (Exception e) {
      return Optional.empty();
    }
  }

  public String getAssessmentName(Long termId) {
    var term = termRepository.findById(termId).orElse(null);
    return term == null ? StringUtils.EMPTY : term.getName();
  }

  private EyReportDto.TableMarks buildTableMarks(Long termId, Student student) {
    var term = termRepository.findById(termId);
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList(term != null ? term.get().getSlug() : "t1"),
            student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var scholasticDataList =
        data.stream()
            .filter(x -> SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory()))
            .toList();
    var sortedData = sortTable(buildTableMarks(scholasticDataList));
    return EyReportDto.TableMarks.builder().firstTableMarks(sortedData.firstTableMarks()).build();
  }

  public List<EyReportDto.Marks> buildTableMarks(List<LowerGradeReportCardData> reportCardData) {
    List<EyReportDto.Marks> marksList = new ArrayList<>();
    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var assessments =
              scholasticData.stream()
                  .map(LowerGradeReportCardData::getAssessmentSlug)
                  .distinct()
                  .sorted()
                  .toList();
          var paper1 =
              getMarks(!assessments.isEmpty() ? assessments.get(0) : "paper1", scholasticData);
          var paper2 =
              getMarks(assessments.size() > 1 ? assessments.get(1) : "paper2", scholasticData);
          var paper3 =
              getMarks(assessments.size() > 2 ? assessments.get(2) : "paper3", scholasticData);
          var paper4 =
              getMarks(assessments.size() > 3 ? assessments.get(3) : "paper4", scholasticData);
          String totalPaper1 =
              getSubjectsMarks(
                  !assessments.isEmpty() ? assessments.get(0) : "paper1", scholasticData);
          String totalPaper2 =
              getSubjectsMarks(
                  assessments.size() > 1 ? assessments.get(1) : "paper2", scholasticData);
          String totalPaper3 =
              getSubjectsMarks(
                  assessments.size() > 2 ? assessments.get(2) : "paper3", scholasticData);
          String totalPaper4 =
              getSubjectsMarks(
                  assessments.size() > 3 ? assessments.get(3) : "paper4", scholasticData);
          var term1SecuredTotal = 0.0;
          var term1TotalMarks = 0.0;
          Double percentage = 0.0;
          Double roundedPercentage = 0.0;
          var absentReason = calculatePercentage(paper1, paper2, paper3, paper4);
          if (absentReason == null) {
            term1SecuredTotal = sumMarks(paper1, paper2, paper3, paper4);
            term1TotalMarks = sumMarks(totalPaper1, totalPaper2, totalPaper3, totalPaper4);
            percentage = term1SecuredTotal / term1TotalMarks * 100;
            roundedPercentage = Math.round(percentage * 100.0) / 100.0;
          }
          var grade = getGrade(paper1, paper2, paper3, paper4, term1SecuredTotal, term1TotalMarks);
          marksList.add(
              EyReportDto.Marks.builder()
                  .subjectSlug(scholasticData.getFirst().getSubjectSlug())
                  .subject(subject)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .paper1(buildPaper1(paper1, totalPaper1))
                  .paper2(buildPaper2(paper2, totalPaper2))
                  .paper3(buildPaper3(paper3, totalPaper3))
                  .paper4(buildPaper4(paper4, totalPaper4))
                  .otdId(scholasticData.get(0).getOtdId())
                  .grade(grade)
                  .percentage(
                      absentReason == null ? String.valueOf(roundedPercentage) : absentReason)
                  .startDate(
                      buildStartDate(
                          List.of("paper1", "mock1", "mock2", "i-a1", "i-a2", "component1"),
                          scholasticData))
                  .build());
        });
    return marksList;
  }

  private String getGrade(
      String paper1,
      String paper2,
      String paper3,
      String paper4,
      double securedTotal,
      double totalMarks) {
    String[] papers = {paper1, paper2, paper3, paper4};
    for (String paper : papers) {
      if ("PA".equals(paper)) {
        return "PA";
      } else if ("ML".equals(paper)) {
        return "ML";
      } else if ("ABS".equals(paper)) {
        return "AB";
      } else if ("AB".equals(paper)) {
        return "AB";
      }
    }
    return calculateGrade(securedTotal, totalMarks);
  }

  private String calculatePercentage(String paper1, String paper2, String paper3, String paper4) {
    String[] papers = {paper1, paper2, paper3, paper4};
    for (String paper : papers) {
      if ("PA".equals(paper)) {
        return "PA";
      } else if ("ML".equals(paper)) {
        return "ML";
      } else if ("ABS".equals(paper)) {
        return "AB";
      } else if ("AB".equals(paper)) {
        return "AB";
      }
    }
    return null;
  }

  private String buildStartDate(
      List<String> assessmentSlugs, List<LowerGradeReportCardData> scholasticData) {
    SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
    return scholasticData.stream()
        .filter(
            data ->
                assessmentSlugs.stream()
                    .anyMatch(slug -> slug.equalsIgnoreCase(data.getAssessmentSlug())))
        .map(LowerGradeReportCardData::getStartDate)
        .filter(Objects::nonNull)
        .findFirst()
        .map(formatter::format)
        .orElse(null);
  }

  private EyReportDto.FirstPageFirstTable buildFirstTable(
      List<EyReportDto.Marks> marks, Student student, Long templateId, String termSlug) {
    var section = student.getSection();
    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlugAndTemplateId(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug(), templateId);
    var reportCardConfig =
        reportCardConfigs.stream()
            .filter(
                rc ->
                    rc.getReportCardConfigDetails().stream()
                        .anyMatch(
                            rccd -> rccd.getTermAssessment().getTerm().getSlug().equals(termSlug)))
            .toList();

    if (reportCardConfig.isEmpty()) {
      log.error("Report card config is empty for organization: {}", section.getOrganization());
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report card config not found");
    }
    var configDetails =
        reportCardConfig.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    if (configDetails.isEmpty()) {
      log.error(
          "No config details found for report card config: {}",
          reportCardConfig.getFirst().getId());
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "No config details found");
    }

    var upperGrades = List.of("ix", "x", "xig", "xiig");

    String column1 = constructColumn(configDetails.get(0));
    String column2 =
        configDetails.size() > 1 ? constructColumn(configDetails.get(1)) : StringUtils.EMPTY;
    String column3 =
        configDetails.size() > 2 ? constructColumn(configDetails.get(2)) : StringUtils.EMPTY;
    String column4 =
        configDetails.size() > 3
            ? constructColumn(configDetails.get(3))
            : upperGrades.contains(section.getGradeSlug()) ? "COMPONENT - 4" : "PAPER - 4";

    return EyReportDto.FirstPageFirstTable.builder()
        .title("SCHOLASTIC SUBJECTS")
        .column1(column1)
        .column2(column2)
        .column3(column3)
        .column4(column4)
        .marks(marks)
        .build();
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? StringUtils.EMPTY
        : configDetail.getTermAssessment().getName();
  }

  private EyReportDto.TableMarks sortTable(List<EyReportDto.Marks> firstTable) {
    List<EyReportDto.Marks> firstTableMarks = new ArrayList<>();

    var sortedFirstTable =
        firstTable.stream().sorted(Comparator.comparingLong(EyReportDto.Marks::seqNo)).toList();

    for (int i = 0; i < sortedFirstTable.size(); i++) {
      EyReportDto.Marks mark = sortedFirstTable.get(i);
      firstTableMarks.add(
          EyReportDto.Marks.builder()
              .sno(i + 1)
              .subject(mark.subject())
              .subjectSlug(mark.subjectSlug())
              .paper1(buildPaper1(mark))
              .paper2(buildPaper2(mark))
              .paper3(buildPaper3(mark))
              .paper4(buildPaper4(mark))
              .percentage(mark.percentage())
              .grade(mark.grade())
              .startDate(mark.startDate())
              .otdId(mark.otdId())
              .build());
    }

    return EyReportDto.TableMarks.builder().firstTableMarks(firstTableMarks).build();
  }

  public EyReportDto.Paper1 buildPaper1(EyReportDto.Marks marks) {
    return EyReportDto.Paper1.builder()
        .maxMarks(marks.paper1().maxMarks())
        .maxSecured(marks.paper1().maxSecured())
        .build();
  }

  public EyReportDto.Paper2 buildPaper2(EyReportDto.Marks marks) {

    return EyReportDto.Paper2.builder()
        .maxMarks(marks.paper2().maxMarks())
        .maxSecured(marks.paper2().maxSecured())
        .build();
  }

  public EyReportDto.Paper3 buildPaper3(EyReportDto.Marks marks) {
    return EyReportDto.Paper3.builder()
        .maxMarks(marks.paper3().maxMarks())
        .maxSecured(marks.paper3().maxSecured())
        .build();
  }

  public EyReportDto.Paper4 buildPaper4(EyReportDto.Marks marks) {
    return EyReportDto.Paper4.builder()
        .maxMarks(marks.paper4().maxMarks())
        .maxSecured(marks.paper4().maxSecured())
        .build();
  }

  private EyReportDto.Paper1 buildPaper1(String marksScored, String total) {

    return EyReportDto.Paper1.builder()
        .maxMarks(String.valueOf(total))
        .maxSecured(String.valueOf(marksScored))
        .build();
  }

  private EyReportDto.Paper2 buildPaper2(String marksScored, String total) {
    return EyReportDto.Paper2.builder()
        .maxMarks(String.valueOf(total))
        .maxSecured(String.valueOf(marksScored))
        .build();
  }

  private EyReportDto.Paper3 buildPaper3(String marksScored, String total) {
    return EyReportDto.Paper3.builder()
        .maxMarks(String.valueOf(total))
        .maxSecured(String.valueOf(marksScored))
        .build();
  }

  private EyReportDto.Paper4 buildPaper4(String marksScored, String total) {
    return EyReportDto.Paper4.builder()
        .maxMarks(String.valueOf(total))
        .maxSecured(String.valueOf(marksScored))
        .build();
  }

  private Optional<SubjectsMetaData> getSubjectMetaData(
      List<SubjectsMetadataStudents> studentsSubjectMetaData, String subjectName) {
    return studentsSubjectMetaData.stream()
        .map(SubjectsMetadataStudents::getSubjectsMetaData)
        .filter(subjectMetaData -> subjectMetaData.getName().equals(subjectName))
        .findFirst();
  }

  private String getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    var data =
        subjectData.stream()
            .filter(d -> assessmentSlug.equalsIgnoreCase(d.getAssessmentSlug()))
            .toList();
    if (data.isEmpty()) {
      return "0.0";
    }
    Double average;
    if (FALSE.equals(data.getFirst().getIsAttended())) {
      return data.getFirst().getRemarks() == null
          ? "ABS"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    } else {
      average =
          data.stream()
              .map(LowerGradeReportCardData::getMarks)
              .filter(Objects::nonNull)
              .mapToDouble(Double::doubleValue)
              .average()
              .orElse(0.0);
    }
    return String.format("%.1f", average);
  }

  private String getSubjectsMarks(
      String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    double average =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .map(LowerGradeReportCardData::getSubjectMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Long::longValue)
            .average()
            .orElse(0.0);

    return String.format("%.1f", average);
  }

  private Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private String calculateGrade(Double marks, Double totalMarks) {
    try {
      return marks == null || Objects.isNull(totalMarks) || Double.isNaN(marks / totalMarks)
          ? null
          : pointScaleEvaluator.evaluate(
              "9point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return null;
    }
  }
}
