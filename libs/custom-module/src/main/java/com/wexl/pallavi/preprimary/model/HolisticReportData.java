package com.wexl.pallavi.preprimary.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "holistic_report_data")
public class HolisticReportData {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "academic_year")
  private String year;

  @Column(name = "school_name")
  private String schoolName;

  private String campus;

  private String board;

  @Column(name = "student_section")
  private String section;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "student_name")
  private String studentName;

  @Column(name = "admission_no")
  private String admissionNo;

  @Column(name = "sms_no")
  private String smsNo;

  @Column(name = "dob")
  private String dob;

  @Column(name = "teacher_name")
  private String teacherName;

  @Column(name = "father_name")
  private String fatherName;

  @Column(name = "father_number")
  private String fatherNumber;

  @Column(name = "mother_name")
  private String motherName;

  @Column(name = "mother_number")
  private String motherNumber;

  @Column(name = "i_like", columnDefinition = "TEXT")
  private String like;

  @Column(name = "i_live")
  private String live;

  @Column(name = "my_friend")
  private String myFriend;

  @Column(name = "my_fev_colour")
  private String myFevColour;

  @Column(name = "my_fev_foods")
  private String myFevFoods;

  @Column(name = "my_fev_games")
  private String myFevGames;

  @Column(name = "my_fev_animals")
  private String myFevAnimals;

  private String address;

  @Column(columnDefinition = "TEXT")
  private String observation;

  @Column(name = "parent_feedback_participating", columnDefinition = "TEXT")
  private String parentFeedbackParticipating;

  @Column(name = "parent_feedback_supported", columnDefinition = "TEXT")
  private String parentFeedbackSupported;

  @Column(name = "parent_feedback_like", columnDefinition = "TEXT")
  private String parentFeedbackLike;

  @Column(name = "parent_feedback_appropriate", columnDefinition = "TEXT")
  private String parentFeedbackAppropriate;

  private String height;

  private String weight;

  @Column(name = "self_assessment_i_enjoy_the_most")
  private String selfAssessmentIEnjoyTheMost;

  @Column(name = "self_assessment_find_difficult")
  private String selfAssessmentFindDifficult;

  @Column(name = "self_assessment_enjoy_doing_with_my_friends")
  private String selfAssessmentEnjoyDoingWithMyFriends;

  @Column(name = "peer_assessment_helps_in_completing")
  private String peerAssessmentHelpInCompleting;

  @Column(name = "peer_assessment_likes_to_play")
  private String peerAssessmentLikeToPlay;

  @Column(name = "peer_assessment_shares_stationery")
  private String peerAssessmentSharesStationery;
}
