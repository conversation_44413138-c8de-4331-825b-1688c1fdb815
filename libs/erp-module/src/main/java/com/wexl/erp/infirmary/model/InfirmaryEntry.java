package com.wexl.erp.infirmary.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "infirmary_entries")
public class InfirmaryEntry extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id", nullable = false)
  private Long studentId;

  @Column(name = "student_name", nullable = false)
  private String studentName;

  @Column(name = "grade", nullable = false)
  private String grade;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Column(name = "date", nullable = false)
  private LocalDate date;

  @Column(name = "in_time", nullable = false)
  private LocalDateTime inTime;

  @Column(name = "out_time", nullable = false)
  private LocalDateTime outTime;

  @Column(name = "remarks")
  private String remarks;
}
