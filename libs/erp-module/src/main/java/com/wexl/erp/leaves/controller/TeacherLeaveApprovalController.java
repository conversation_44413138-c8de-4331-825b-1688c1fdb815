package com.wexl.erp.leaves.controller;

import com.wexl.erp.leaves.dto.StudentLeaveRequestDto;
import com.wexl.erp.leaves.dto.StudentSearchDto;
import com.wexl.erp.leaves.service.LeaveService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/orgs/{orgSlug}/teachers/{authUserId}/leaves")
public class TeacherLeaveApprovalController {

  private final LeaveService leaveService;

  @GetMapping
  public List<StudentLeaveRequestDto.Response> getPendingLeaveRequests(
      @PathVariable String authUserId,
      @RequestParam(required = false) String gradeSlug,
      @RequestParam(required = false) String sectionName) {
    try {
      return leaveService.getAllLeaveRequestsSorted(authUserId, gradeSlug, sectionName);
    } catch (Exception e) {
      log.error("Error while fetching pending leave requests: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/{leaveId}/approval")
  @ResponseStatus(HttpStatus.CREATED)
  public void approveOrRejectLeaveRequest(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      @PathVariable Long leaveId,
      @Valid @RequestBody StudentLeaveRequestDto.ApprovalRequest request) {
    try {
      leaveService.approveOrRejectLeaveRequest(orgSlug, authUserId, leaveId, request);
    } catch (Exception e) {
      log.error("Error while approving/rejecting leave request: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/search")
  public List<StudentLeaveRequestDto.Response> findStudentLeaveByName(
      @RequestBody StudentSearchDto.Request request,
      @PathVariable String authUserId,
      @PathVariable String orgSlug) {
    return leaveService.getStudentLeaveRequestsByStudentName(request, authUserId, orgSlug);
  }
}
