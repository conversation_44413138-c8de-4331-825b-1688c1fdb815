package com.wexl.erp.infirmary.repository;

import com.wexl.erp.infirmary.model.InfirmaryEntry;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface InfirmaryRepository extends JpaRepository<InfirmaryEntry, Long> {
  List<InfirmaryEntry> findAllByStudentId(Long studentId);

  List<InfirmaryEntry> findAllByOrgSlug(String orgSlug);
}
