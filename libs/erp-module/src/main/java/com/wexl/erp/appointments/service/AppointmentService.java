package com.wexl.erp.appointments.service;

import com.wexl.erp.appointments.dto.AppointmentSearchDto;
import com.wexl.erp.appointments.dto.ParentAppointmentDto;
import com.wexl.erp.appointments.model.Appointment;
import com.wexl.erp.appointments.model.AppointmentStatus;
import com.wexl.erp.appointments.repository.AppointmentRepository;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.staff.repository.StaffRepository;
import com.wexl.retail.telegram.service.UserService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@EqualsAndHashCode
@Service
@Slf4j
@RequiredArgsConstructor
public class AppointmentService {

  private final AppointmentRepository appointmentsRepository;
  private final NotificationsService notificationsService;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherSectionRepository teacherSectionRepository;
  private final StudentRepository studentRepository;
  private final UserRepository userRepository;
  private final GuardianRepository guardianRepository;
  private final UserService userService;
  private final StaffRepository staffRepository;
  private final GuardianService guardianService;

  private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

  private ParentAppointmentDto.Response buildAppointmentResponse(Appointment appointment) {
    Optional<User> user = userRepository.findByAuthUserId(appointment.getReviewedBy());
    return ParentAppointmentDto.Response.builder()
        .appointmentId(appointment.getId())
        .studentId(appointment.getStudent().getId())
        .guardianId(appointment.getGuardian().getId())
        .studentName(userService.getNameByUserInfo(appointment.getStudent().getUserInfo()))
        .gradeName(appointment.getStudent().getSection().getGradeName())
        .gradeSlug(appointment.getStudent().getSection().getGradeSlug())
        .studentSection(appointment.getStudent().getSection().getName())
        .appointmentDate(DateTimeUtil.convertIso8601ToEpoch(appointment.getAppointmentDate()))
        .appointmentReason(appointment.getAppointmentReason())
        .status(appointment.getStatus())
        .appliedDate(DateTimeUtil.convertIso8601ToEpoch(appointment.getAppliedDate()))
        .reviewedBy(userService.getNameByUserInfo(user.get().getTeacherInfo().getUserInfo()))
        .reviewedOn(
            appointment.getReviewedOn() != null
                ? DateTimeUtil.convertIso8601ToEpoch(appointment.getReviewedOn())
                : null)
        .build();
  }

  private Appointment buildAppointmentRequest(ParentAppointmentDto.Request request) {
    return Appointment.builder()
        .appointmentDate(dateTimeUtil.convertEpochToIso8601(request.appointmentDate()))
        .appointmentReason(request.appointmentReason())
        .status(AppointmentStatus.PENDING)
        .appliedDate(LocalDateTime.now())
        .build();
  }

  @Transactional
  public ParentAppointmentDto.Response applyForAppointment(
      String orgSlug, String guardianAuthId, ParentAppointmentDto.Request request) {

    Optional<Student> students = studentRepository.findById(request.studentId());
    if (students.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found for this user");
    }
    Student student = students.get();

    Guardian guardian = getValidatedGuardianForStudent(student);

    Appointment appointment = buildAppointmentRequest(request);
    appointment.setStudent(student);
    appointment.setGuardian(guardian);
    appointment.setOrgSlug(orgSlug);

    Appointment savedAppointment = appointmentsRepository.save(appointment);

    var sectionTeachers = teacherSectionRepository.findAllBySection(student.getSection());
    if (sectionTeachers.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, "No teachers found for this section");
    }

    LocalDateTime appliedDate = LocalDateTime.now();

    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title("New Appointment Request")
            .message(
                guardian.getFirstName()
                    + " "
                    + guardian.getLastName()
                    + " has requested an appointment for "
                    + student.getUserInfo().getFirstName()
                    + " "
                    + student.getUserInfo().getLastName()
                    + " on "
                    + appointment.getAppointmentDate().format(formatter)
                    + ". Reason: "
                    + appointment.getAppointmentReason()
                    + ". Applied on: "
                    + appliedDate.format(formatter))
            .notificationType(NotificationType.APPOINTMENT_REQUEST)
            .build();

    String teacherAuthId =
        sectionTeachers.isEmpty()
            ? null
            : sectionTeachers.get(0).getTeacher().getUserInfo().getAuthUserId();

    notificationsService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);

    log.info("Appointment request created with ID: {}", savedAppointment.getId());
    return buildAppointmentResponse(savedAppointment);
  }

  public List<ParentAppointmentDto.Response> getGuardianAppointmentRequests(
      String orgSlug, String guardianAuthId) {

    User user =
        userRepository
            .findByAuthUserId(guardianAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "User not found"));

    Student student = user.getStudentInfo();
    if (student == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found");
    }

    Guardian guardian = getValidatedGuardianForStudent(student);

    List<Appointment> appointments =
        appointmentsRepository.findByOrgSlugAndGuardianOrderByAppliedDateDescStatusAsc(
            orgSlug, guardian);

    return appointments.stream().map(this::buildAppointmentResponse).collect(Collectors.toList());
  }

  @Transactional
  public ParentAppointmentDto.Response updateAppointmentRequest(
      String orgSlug,
      String guardianAuthId,
      Long appointmentId,
      ParentAppointmentDto.Request request) {

    Appointment appointment = getAppointmentById(appointmentId);

    Optional<Student> studentOptional = studentRepository.findById(request.studentId());
    if (studentOptional.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found for this user");
    }
    Student student = studentOptional.get();

    Guardian guardian = getValidatedGuardianForStudent(student);
    if (guardian == null) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, "Guardian not found for this student");
    }

    appointment.setAppointmentDate(dateTimeUtil.convertEpochToIso8601(request.appointmentDate()));
    appointment.setAppointmentReason(request.appointmentReason());

    Appointment updatedAppointment = appointmentsRepository.save(appointment);
    return buildAppointmentResponse(updatedAppointment);
  }

  @Transactional
  public void deleteAppointmentRequest(Long appointmentId) {
    appointmentsRepository.deleteById(appointmentId);
  }

  @Transactional
  public void approveOrRejectAppointmentRequest(
      String orgSlug,
      String teacherAuthId,
      Long appointmentId,
      ParentAppointmentDto.ApprovalRequest approvalRequest) {

    Appointment appointment = getAppointmentById(appointmentId);

    if (appointment.getStatus() != AppointmentStatus.PENDING) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Cannot update an appointment request that has already been Approved or Disapproved: "
              + appointment.getStatus().toString().toLowerCase());
    }

    appointment.setStatus(approvalRequest.status());
    appointment.setReviewedBy(teacherAuthId);
    appointment.setReviewedOn(LocalDateTime.now());

    appointmentsRepository.save(appointment);

    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title("Appointment Request " + approvalRequest.status())
            .message(
                "Your appointment request for "
                    + appointment.getStudent().getUserInfo().getFirstName()
                    + " "
                    + appointment.getStudent().getUserInfo().getLastName()
                    + " on "
                    + appointment.getAppointmentDate().format(formatter)
                    + " has been "
                    + approvalRequest.status().toString().toLowerCase())
            .notificationType(
                approvalRequest.status() == AppointmentStatus.APPROVED
                    ? NotificationType.APPOINTMENT_APPROVED
                    : NotificationType.APPOINTMENT_DISAPPROVED)
            .studentIds(List.of(appointment.getStudent().getId()))
            .build();

    notificationsService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);
  }

  public List<ParentAppointmentDto.Response> getAppointmentRequestsByStudentName(
      String searchKey, AppointmentSearchDto.Request request, String authUserId, String orgSlug) {
    List<User> matchingUsers =
        userRepository.findByOrgSlugAndSearchKey(orgSlug, "%" + searchKey + "%");
    User authUser =
        userRepository
            .findByAuthUserId(authUserId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "User not found"));

    boolean isAdmin = UserRoleHelper.get().isOrgAdmin(authUser);
    List<Long> teacherSectionList =
        isAdmin
            ? null
            : authUser.getTeacherInfo().getSections().stream().map(Section::getId).toList();

    List<Student> students =
        matchingUsers.stream()
            .filter(user -> user.getStudentInfo() != null)
            .map(User::getStudentInfo)
            .toList();

    List<Appointment> appointments =
        students.stream()
            .flatMap(student -> appointmentsRepository.findByStudent(student).stream())
            .collect(Collectors.toList());

    if (!isAdmin && teacherSectionList != null) {
      appointments =
          appointments.stream()
              .filter(
                  appointment ->
                      teacherSectionList.contains(appointment.getStudent().getSection().getId()))
              .collect(Collectors.toList());
    }

    return appointments.stream().map(this::buildAppointmentResponse).collect(Collectors.toList());
  }

  public List<ParentAppointmentDto.Response> getAllAppointmentRequestsSorted(
      String authUserId, String gradeSlug, String sectionName) {
    var user =
        userRepository
            .findByAuthUserId(authUserId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "User not found"));
    if (UserRoleHelper.get().isOrgAdmin(user)) {
      return appointmentsRepository.findAllByOrderByAppliedDateDescStatusAsc().stream()
          .map(this::buildAppointmentResponse)
          .filter(
              response ->
                  (Objects.isNull(gradeSlug) || Objects.isNull(sectionName))
                      || (response.gradeSlug().equalsIgnoreCase(gradeSlug)
                          && response.studentSection().equalsIgnoreCase(sectionName)))
          .collect(Collectors.toList());
    } else {
      List<Long> teacherSectionIds =
          user.getTeacherInfo().getSections().stream().map(Section::getId).toList();
      List<Appointment> pendingRequests =
          appointmentsRepository.findBySectionIds(teacherSectionIds);
      return pendingRequests.stream()
          .map(this::buildAppointmentResponse)
          .filter(
              response ->
                  (Objects.isNull(gradeSlug) || Objects.isNull(sectionName))
                      || (response.gradeSlug().equalsIgnoreCase(gradeSlug)
                          && response.studentSection().equalsIgnoreCase(sectionName)))
          .collect(Collectors.toList());
    }
  }

  public Appointment getAppointmentById(Long appointmentId) {
    return appointmentsRepository
        .findById(appointmentId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.NO_RECORD_FOUND, "Appointment request not found"));
  }

  public Guardian getValidatedGuardianForStudent(Student student) {
    List<Guardian> guardians = student.getGuardians();
    Guardian guardian = null;

    if (guardians != null && !guardians.isEmpty()) {
      for (Guardian g : guardians) {
        if (Boolean.TRUE.equals(g.getIsPrimary())) {
          guardian = g;
          break;
        }
      }

      if (guardian == null) {
        guardian = guardians.get(0);
      }
    } else {
      guardian = guardianRepository.findByStudentId(student.getId());
    }

    if (guardian == null) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, "Guardian not found for this student");
    }

    if (guardian.getStudent().getId() != (student.getId())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Guardian is not related to this student");
    }

    return guardian;
  }

  public AppointmentSearchDto.Response getTeachersOrStaff(
      String orgSlug, String authUserId, String role) {
    var user = guardianService.validateUser(authUserId);

    if (role.equalsIgnoreCase("TEACHER")) {
      var teacherSections =
          teacherSectionRepository.findAllBySection(user.getStudentInfo().getSection());

      if (teacherSections.isEmpty()) {
        return null;
      }

      var teachers =
          teacherSections.stream()
              .map(
                  ts -> {
                    var teacher = ts.getTeacher();
                    var info = teacher.getUserInfo();
                    return new AppointmentSearchDto.TeacherResponse(
                        info.getFirstName() + " " + info.getLastName(),
                        teacher.getId(),
                        info.getAuthUserId());
                  })
              .distinct()
              .toList();

      return AppointmentSearchDto.Response.builder()
          .teacherResponse(teachers)
          .staffResponse(Collections.emptyList())
          .build();
    }

    var staffs =
        staffRepository.getDesignationsByOrg(orgSlug).stream()
            .map(
                staff -> {
                  var info = staff.getUser();
                  return new AppointmentSearchDto.StaffResponse(
                      staff.getDesignation().getName(),
                      staff.getDesignation().getId(),
                      info.getId(),
                      info.getAuthUserId());
                })
            .toList();

    return AppointmentSearchDto.Response.builder()
        .staffResponse(staffs)
        .teacherResponse(Collections.emptyList())
        .build();
  }
}
