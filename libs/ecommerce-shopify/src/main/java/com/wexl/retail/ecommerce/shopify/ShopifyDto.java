package com.wexl.retail.ecommerce.shopify;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public record ShopifyDto() {

  public record ProductResponse(@JsonProperty("products") List<Products> products) {}

  public record Products(
      String title,
      Long id,
      @JsonProperty("body_html") String description,
      String status,
      @JsonProperty("image") Image image) {}

  public record Image(@JsonProperty("src") String src) {}

  public record OrdersResponse(@JsonProperty("orders") List<Orders> Orders) {}

  public record Orders(
      Long id,
      @JsonProperty("customer") Customer Customer,
      @JsonProperty("line_items") List<LineItems> LineItems) {}

  public record Customer(
      Long id,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("default_address") Address defaultAddress,
      String email) {}

  public record Address(String phone) {}

  public record LineItems(Long id, String title, @JsonProperty("product_id") String productId) {}
}
